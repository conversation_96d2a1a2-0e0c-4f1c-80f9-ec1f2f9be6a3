{"name": "madad", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "madad", "version": "1.0.0", "license": "ISC", "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "firebase": "^11.0.2"}}, "node_modules/@firebase/analytics": {"version": "0.10.10", "resolved": "https://registry.npmjs.org/@firebase/analytics/-/analytics-0.10.10.tgz", "integrity": "sha512-Psdo7c9g2SLAYh6u1XRA+RZ7ab2JfBVuAt/kLzXkhKZL/gS2cQUCMsOW5p0RIlDPRKqpdNSmvujd2TeRWLKOkQ==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/installations": "0.6.11", "@firebase/logger": "0.4.4", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/analytics-compat": {"version": "0.2.16", "resolved": "https://registry.npmjs.org/@firebase/analytics-compat/-/analytics-compat-0.2.16.tgz", "integrity": "sha512-Q/s+u/TEMSb2EDJFQMGsOzpSosybBl8HuoSEMyGZ99+0Pu7SIR9MPDGUjc8PKiCFQWDJ3QXxgqh1d/rujyAMbA==", "license": "Apache-2.0", "dependencies": {"@firebase/analytics": "0.10.10", "@firebase/analytics-types": "0.8.3", "@firebase/component": "0.6.11", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/analytics-types": {"version": "0.8.3", "resolved": "https://registry.npmjs.org/@firebase/analytics-types/-/analytics-types-0.8.3.tgz", "integrity": "sha512-VrIp/d8iq2g501qO46uGz3hjbDb8xzYMrbu8Tp0ovzIzrvJZ2fvmj649gTjge/b7cCCcjT0H37g1gVtlNhnkbg==", "license": "Apache-2.0"}, "node_modules/@firebase/app": {"version": "0.10.16", "resolved": "https://registry.npmjs.org/@firebase/app/-/app-0.10.16.tgz", "integrity": "sha512-SUati2qH48gvVGnSsqMkZr1Iq7No52a3tJQ4itboSTM89Erezmw3v1RsfVymrDze9+KiOLmBpvLNKSvheITFjg==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/logger": "0.4.4", "@firebase/util": "1.10.2", "idb": "7.1.1", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@firebase/app-check": {"version": "0.8.10", "resolved": "https://registry.npmjs.org/@firebase/app-check/-/app-check-0.8.10.tgz", "integrity": "sha512-DWFfxxif/t+Ow4MmRUevDX+A3hVxm1rUf6y5ZP4sIomfnVCO1NNahqtsv9rb1/tKGkTeoVT40weiTS/WjQG1mA==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/logger": "0.4.4", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/app-check-compat": {"version": "0.3.17", "resolved": "https://registry.npmjs.org/@firebase/app-check-compat/-/app-check-compat-0.3.17.tgz", "integrity": "sha512-a/eadrGsY0MVCBPhrNbKUhoYpms4UKTYLKO7nswwSFVsm3Rw6NslQQCNLfvljcDqP4E7alQDRGJXjkxd/5gJ+Q==", "license": "Apache-2.0", "dependencies": {"@firebase/app-check": "0.8.10", "@firebase/app-check-types": "0.5.3", "@firebase/component": "0.6.11", "@firebase/logger": "0.4.4", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/app-check-interop-types": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/@firebase/app-check-interop-types/-/app-check-interop-types-0.3.3.tgz", "integrity": "sha512-gAlxfPLT2j8bTI/qfe3ahl2I2YcBQ8cFIBdhAQA4I2f3TndcO+22YizyGYuttLHPQEpWkhmpFW60VCFEPg4g5A==", "license": "Apache-2.0"}, "node_modules/@firebase/app-check-types": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/@firebase/app-check-types/-/app-check-types-0.5.3.tgz", "integrity": "sha512-hyl5rKSj0QmwPdsAxrI5x1otDlByQ7bvNvVt8G/XPO2CSwE++rmSVf3VEhaeOR4J8ZFaF0Z0NDSmLejPweZ3ng==", "license": "Apache-2.0"}, "node_modules/@firebase/app-compat": {"version": "0.2.46", "resolved": "https://registry.npmjs.org/@firebase/app-compat/-/app-compat-0.2.46.tgz", "integrity": "sha512-9hSHWE5LMqtKIm13CnH5OZeMPbkVV3y5vgNZ5EMFHcG2ceRrncyNjG9No5XfWQw8JponZdGs4HlE4aMD/jxcFA==", "license": "Apache-2.0", "dependencies": {"@firebase/app": "0.10.16", "@firebase/component": "0.6.11", "@firebase/logger": "0.4.4", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@firebase/app-types": {"version": "0.9.3", "resolved": "https://registry.npmjs.org/@firebase/app-types/-/app-types-0.9.3.tgz", "integrity": "sha512-kRVpIl4vVGJ4baogMDINbyrIOtOxqhkZQg4jTq3l8Lw6WSk0xfpEYzezFu+Kl4ve4fbPl79dvwRtaFqAC/ucCw==", "license": "Apache-2.0"}, "node_modules/@firebase/auth": {"version": "1.8.1", "resolved": "https://registry.npmjs.org/@firebase/auth/-/auth-1.8.1.tgz", "integrity": "sha512-LX9N/Cf5Z35r5yqm2+5M3+2bRRe/+RFaa/+u4HDni7TA27C/Xm4XHLKcWcLg1BzjrS4zngSaBEOSODvp6RFOqQ==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/logger": "0.4.4", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app": "0.x", "@react-native-async-storage/async-storage": "^1.18.1"}, "peerDependenciesMeta": {"@react-native-async-storage/async-storage": {"optional": true}}}, "node_modules/@firebase/auth-compat": {"version": "0.5.16", "resolved": "https://registry.npmjs.org/@firebase/auth-compat/-/auth-compat-0.5.16.tgz", "integrity": "sha512-YlYwJMBqAyv0ESy3jDUyshMhZlbUiwAm6B6+uUmigNDHU+uq7j4SFiDJEZlFFIz397yBzKn06SUdqutdQzGnCA==", "license": "Apache-2.0", "dependencies": {"@firebase/auth": "1.8.1", "@firebase/auth-types": "0.12.3", "@firebase/component": "0.6.11", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/auth-interop-types": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/@firebase/auth-interop-types/-/auth-interop-types-0.2.4.tgz", "integrity": "sha512-JPgcXKCuO+CWqGDnigBtvo09HeBs5u/Ktc2GaFj2m01hLarbxthLNm7Fk8iOP1aqAtXV+fnnGj7U28xmk7IwVA==", "license": "Apache-2.0"}, "node_modules/@firebase/auth-types": {"version": "0.12.3", "resolved": "https://registry.npmjs.org/@firebase/auth-types/-/auth-types-0.12.3.tgz", "integrity": "sha512-Zq9zI0o5hqXDtKg6yDkSnvMCMuLU6qAVS51PANQx+ZZX5xnzyNLEBO3GZgBUPsV5qIMFhjhqmLDxUqCbnAYy2A==", "license": "Apache-2.0", "peerDependencies": {"@firebase/app-types": "0.x", "@firebase/util": "1.x"}}, "node_modules/@firebase/component": {"version": "0.6.11", "resolved": "https://registry.npmjs.org/@firebase/component/-/component-0.6.11.tgz", "integrity": "sha512-eQbeCgPukLgsKD0Kw5wQgsMDX5LeoI1MIrziNDjmc6XDq5ZQnuUymANQgAb2wp1tSF9zDSXyxJmIUXaKgN58Ug==", "license": "Apache-2.0", "dependencies": {"@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@firebase/data-connect": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/@firebase/data-connect/-/data-connect-0.1.2.tgz", "integrity": "sha512-Bcf29mntFCt5V7aceMe36wnkHrG7cwbMlUVbDHOlh2foQKx9VtSXEONw9r6FtL1sFobHVYOM5L6umX35f59m5g==", "license": "Apache-2.0", "dependencies": {"@firebase/auth-interop-types": "0.2.4", "@firebase/component": "0.6.11", "@firebase/logger": "0.4.4", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/database": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/@firebase/database/-/database-1.0.10.tgz", "integrity": "sha512-sWp2g92u7xT4BojGbTXZ80iaSIaL6GAL0pwvM0CO/hb0nHSnABAqsH7AhnWGsGvXuEvbPr7blZylPaR9J+GSuQ==", "license": "Apache-2.0", "dependencies": {"@firebase/app-check-interop-types": "0.3.3", "@firebase/auth-interop-types": "0.2.4", "@firebase/component": "0.6.11", "@firebase/logger": "0.4.4", "@firebase/util": "1.10.2", "faye-websocket": "0.11.4", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@firebase/database-compat": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/@firebase/database-compat/-/database-compat-2.0.1.tgz", "integrity": "sha512-IsFivOjdE1GrjTeKoBU/ZMenESKDXidFDzZzHBPQ/4P20ptGdrl3oLlWrV/QJqJ9lND4IidE3z4Xr5JyfUW1vg==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/database": "1.0.10", "@firebase/database-types": "1.0.7", "@firebase/logger": "0.4.4", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@firebase/database-types": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/@firebase/database-types/-/database-types-1.0.7.tgz", "integrity": "sha512-I7zcLfJXrM0WM+ksFmFdAMdlq/DFmpeMNa+/GNsLyFo5u/lX5zzkPzGe3srVWqaBQBY5KprylDGxOsP6ETfL0A==", "license": "Apache-2.0", "dependencies": {"@firebase/app-types": "0.9.3", "@firebase/util": "1.10.2"}}, "node_modules/@firebase/firestore": {"version": "4.7.5", "resolved": "https://registry.npmjs.org/@firebase/firestore/-/firestore-4.7.5.tgz", "integrity": "sha512-OO3rHvjC07jL2ITN255xH/UzCVSvh6xG8oTzQdFScQvFbcm1fjCL1hgAdpDZcx3vVcKMV+6ktr8wbllkB8r+FQ==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/logger": "0.4.4", "@firebase/util": "1.10.2", "@firebase/webchannel-wrapper": "1.0.3", "@grpc/grpc-js": "~1.9.0", "@grpc/proto-loader": "^0.7.8", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/firestore-compat": {"version": "0.3.40", "resolved": "https://registry.npmjs.org/@firebase/firestore-compat/-/firestore-compat-0.3.40.tgz", "integrity": "sha512-18HopMN811KYBc9Ptpr1Rewwio0XF09FF3jc5wtV6rGyAs815SlFFw5vW7ZeLd43zv9tlEc2FzM0H+5Vr9ZRxw==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/firestore": "4.7.5", "@firebase/firestore-types": "3.0.3", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/firestore-types": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/@firebase/firestore-types/-/firestore-types-3.0.3.tgz", "integrity": "sha512-hD2jGdiWRxB/eZWF89xcK9gF8wvENDJkzpVFb4aGkzfEaKxVRD1kjz1t1Wj8VZEp2LCB53Yx1zD8mrhQu87R6Q==", "license": "Apache-2.0", "peerDependencies": {"@firebase/app-types": "0.x", "@firebase/util": "1.x"}}, "node_modules/@firebase/functions": {"version": "0.11.10", "resolved": "https://registry.npmjs.org/@firebase/functions/-/functions-0.11.10.tgz", "integrity": "sha512-TP+Dzebazhw6+GduBdWn1kOJRFH84G2z+BW3pNVfkpFRkc//+uT1Uw2+dLpMGSSBRG7FrcDG91vcPnOFCzr15w==", "license": "Apache-2.0", "dependencies": {"@firebase/app-check-interop-types": "0.3.3", "@firebase/auth-interop-types": "0.2.4", "@firebase/component": "0.6.11", "@firebase/messaging-interop-types": "0.2.3", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/functions-compat": {"version": "0.3.16", "resolved": "https://registry.npmjs.org/@firebase/functions-compat/-/functions-compat-0.3.16.tgz", "integrity": "sha512-FL7EXehiiBisNIR7mlb0i+moyWKLVfcEJgh/Wq6ZV6BdrCObpCTz7w5EvuRIEFX5e9cNL2oWInKg8S5X4HtINg==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/functions": "0.11.10", "@firebase/functions-types": "0.6.3", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/functions-types": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/@firebase/functions-types/-/functions-types-0.6.3.tgz", "integrity": "sha512-EZoDKQLUHFKNx6VLipQwrSMh01A1SaL3Wg6Hpi//x6/fJ6Ee4hrAeswK99I5Ht8roiniKHw4iO0B1Oxj5I4plg==", "license": "Apache-2.0"}, "node_modules/@firebase/installations": {"version": "0.6.11", "resolved": "https://registry.npmjs.org/@firebase/installations/-/installations-0.6.11.tgz", "integrity": "sha512-w8fY8mw6fxJzsZM2ufmTtomopXl1+bn/syYon+Gpn+0p0nO1cIUEVEFrFazTLaaL9q1CaVhc3HmseRTsI3igAA==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/util": "1.10.2", "idb": "7.1.1", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/installations-compat": {"version": "0.2.11", "resolved": "https://registry.npmjs.org/@firebase/installations-compat/-/installations-compat-0.2.11.tgz", "integrity": "sha512-SHRgw5LTa6v8LubmJZxcOCwEd1MfWQPUtKdiuCx2VMWnapX54skZd1PkQg0K4l3k+4ujbI2cn7FE6Li9hbChBw==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/installations": "0.6.11", "@firebase/installations-types": "0.5.3", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/installations-types": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/@firebase/installations-types/-/installations-types-0.5.3.tgz", "integrity": "sha512-2FJI7gkLqIE0iYsNQ1P751lO3hER+Umykel+TkLwHj6plzWVxqvfclPUZhcKFVQObqloEBTmpi2Ozn7EkCABAA==", "license": "Apache-2.0", "peerDependencies": {"@firebase/app-types": "0.x"}}, "node_modules/@firebase/logger": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/@firebase/logger/-/logger-0.4.4.tgz", "integrity": "sha512-mH0PEh1zoXGnaR8gD1DeGeNZtWFKbnz9hDO91dIml3iou1gpOnLqXQ2dJfB71dj6dpmUjcQ6phY3ZZJbjErr9g==", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@firebase/messaging": {"version": "0.12.14", "resolved": "https://registry.npmjs.org/@firebase/messaging/-/messaging-0.12.14.tgz", "integrity": "sha512-cS<PERSON>34jJswFvME8tdMDkvJvW6T1jEekyMSyq84AMBZ0KEpJbDWuC9n4wKT2lxUm1jaL651iZnn6g51yCl77ICg==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/installations": "0.6.11", "@firebase/messaging-interop-types": "0.2.3", "@firebase/util": "1.10.2", "idb": "7.1.1", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/messaging-compat": {"version": "0.2.14", "resolved": "https://registry.npmjs.org/@firebase/messaging-compat/-/messaging-compat-0.2.14.tgz", "integrity": "sha512-r9weK8jTEA2aGiwy0IbMQPnzuJ0DHkOQaMxGJOlU2QZ1a7fh6RHpNtaoM+LKnn6u1NQgmAOWYNr9vezVQEm9zw==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/messaging": "0.12.14", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/messaging-interop-types": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@firebase/messaging-interop-types/-/messaging-interop-types-0.2.3.tgz", "integrity": "sha512-xfzFaJpzcmtDjycpDeCUj0Ge10ATFi/VHVIvEEjDNc3hodVBQADZ7BWQU7CuFpjSHE+eLuBI13z5F/9xOoGX8Q==", "license": "Apache-2.0"}, "node_modules/@firebase/performance": {"version": "0.6.11", "resolved": "https://registry.npmjs.org/@firebase/performance/-/performance-0.6.11.tgz", "integrity": "sha512-FlkJFeqLlIeh5T4Am3uE38HVzggliDIEFy/fErEc1faINOUFCb6vQBEoNZGaXvRnTR8lh3X/hP7tv37C7BsK9g==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/installations": "0.6.11", "@firebase/logger": "0.4.4", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/performance-compat": {"version": "0.2.11", "resolved": "https://registry.npmjs.org/@firebase/performance-compat/-/performance-compat-0.2.11.tgz", "integrity": "sha512-DqeNBy51W2xzlklyC7Ht9JQ94HhTA08PCcM4MDeyG/ol3fqum/+YgtHWQ2IQuduqH9afETthZqLwCZiSgY7hiA==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/logger": "0.4.4", "@firebase/performance": "0.6.11", "@firebase/performance-types": "0.2.3", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/performance-types": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@firebase/performance-types/-/performance-types-0.2.3.tgz", "integrity": "sha512-IgkyTz6QZVPAq8GSkLYJvwSLr3LS9+V6vNPQr0x4YozZJiLF5jYixj0amDtATf1X0EtYHqoPO48a9ija8GocxQ==", "license": "Apache-2.0"}, "node_modules/@firebase/remote-config": {"version": "0.4.11", "resolved": "https://registry.npmjs.org/@firebase/remote-config/-/remote-config-0.4.11.tgz", "integrity": "sha512-9z0rgKuws2nj+7cdiqF+NY1QR4na6KnuOvP+jQvgilDOhGtKOcCMq5XHiu66i73A9kFhyU6QQ2pHXxcmaq1pBw==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/installations": "0.6.11", "@firebase/logger": "0.4.4", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/remote-config-compat": {"version": "0.2.11", "resolved": "https://registry.npmjs.org/@firebase/remote-config-compat/-/remote-config-compat-0.2.11.tgz", "integrity": "sha512-zfIjpwPrGuIOZDmduukN086qjhZ1LnbJi/iYzgua+2qeTlO0XdlE1v66gJPwygGB3TOhT0yb9EiUZ3nBNttMqg==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/logger": "0.4.4", "@firebase/remote-config": "0.4.11", "@firebase/remote-config-types": "0.3.3", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/remote-config-types": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/@firebase/remote-config-types/-/remote-config-types-0.3.3.tgz", "integrity": "sha512-YlRI9CHxrk3lpQuFup9N1eohpwdWayKZUNZ/YeQ0PZoncJ66P32UsKUKqVXOaieTjJIOh7yH8JEzRdht5s+d6g==", "license": "Apache-2.0"}, "node_modules/@firebase/storage": {"version": "0.13.4", "resolved": "https://registry.npmjs.org/@firebase/storage/-/storage-0.13.4.tgz", "integrity": "sha512-b1KaTTRiMupFurIhpGIbReaWev0k5O3ouTHkAPcEssT+FvU3q/1JwzvkX4+ZdB60Fc43Mbp8qQ1gWfT0Z2FP9Q==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/storage-compat": {"version": "0.3.14", "resolved": "https://registry.npmjs.org/@firebase/storage-compat/-/storage-compat-0.3.14.tgz", "integrity": "sha512-Ok5FmXJiapaNAOQ8W8qppnfwgP8540jw2B8M0c4TFZqF4BD+CoKBxW0dRtOuLNGadLhzqqkDZZZtkexxrveQqA==", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.11", "@firebase/storage": "0.13.4", "@firebase/storage-types": "0.8.3", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/storage-types": {"version": "0.8.3", "resolved": "https://registry.npmjs.org/@firebase/storage-types/-/storage-types-0.8.3.tgz", "integrity": "sha512-+Muk7g9uwngTpd8xn9OdF/D48uiQ7I1Fae7ULsWPuKoCH3HU7bfFPhxtJYzyhjdniowhuDpQcfPmuNRAqZEfvg==", "license": "Apache-2.0", "peerDependencies": {"@firebase/app-types": "0.x", "@firebase/util": "1.x"}}, "node_modules/@firebase/util": {"version": "1.10.2", "resolved": "https://registry.npmjs.org/@firebase/util/-/util-1.10.2.tgz", "integrity": "sha512-qnSHIoE9FK+HYnNhTI8q14evyqbc/vHRivfB4TgCIUOl4tosmKSQlp7ltymOlMP4xVIJTg5wrkfcZ60X4nUf7Q==", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@firebase/vertexai": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@firebase/vertexai/-/vertexai-1.0.1.tgz", "integrity": "sha512-f48MGSofhaS05ebpN7zMIv4tBqYf19pXr5/4njKtNZVLbjxUswDma0SuFDoO+IwgbdkhFxgtNctM+C1zfI/O1Q==", "license": "Apache-2.0", "dependencies": {"@firebase/app-check-interop-types": "0.3.3", "@firebase/component": "0.6.11", "@firebase/logger": "0.4.4", "@firebase/util": "1.10.2", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app": "0.x", "@firebase/app-types": "0.x"}}, "node_modules/@firebase/webchannel-wrapper": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@firebase/webchannel-wrapper/-/webchannel-wrapper-1.0.3.tgz", "integrity": "sha512-2xCRM9q9FlzGZCdgDMJwc0gyUkWFtkosy7Xxr6sFgQwn+wMNIWd7xIvYNauU1r64B5L5rsGKy/n9TKJ0aAFeqQ==", "license": "Apache-2.0"}, "node_modules/@fortawesome/fontawesome-free": {"version": "6.7.2", "resolved": "https://registry.npmjs.org/@fortawesome/fontawesome-free/-/fontawesome-free-6.7.2.tgz", "integrity": "sha512-JUOtgFW6k9u4Y+xeIaEiLr3+cjoUPiAuLXoyKOJSia6Duzb7pq+A76P9ZdPDoAoxHdHzq6gE9/jKBGXlZT8FbA==", "license": "(CC-BY-4.0 AND OFL-1.1 AND MIT)", "engines": {"node": ">=6"}}, "node_modules/@grpc/grpc-js": {"version": "1.9.15", "resolved": "https://registry.npmjs.org/@grpc/grpc-js/-/grpc-js-1.9.15.tgz", "integrity": "sha512-nqE7Hc0AzI+euzUwDAy0aY5hCp10r734gMGRdU+qOPX0XSceI2ULrcXB5U2xSc5VkWwalCj4M7GzCAygZl2KoQ==", "license": "Apache-2.0", "dependencies": {"@grpc/proto-loader": "^0.7.8", "@types/node": ">=12.12.47"}, "engines": {"node": "^8.13.0 || >=10.10.0"}}, "node_modules/@grpc/proto-loader": {"version": "0.7.13", "resolved": "https://registry.npmjs.org/@grpc/proto-loader/-/proto-loader-0.7.13.tgz", "integrity": "sha512-AiXO/bfe9bmxBjxxtYxFAXGZvMaN5s8kO+jBHAJCON8rJoB5YS/D6X7ZNc6XQkuHNmyl4CYaMI1fJ/Gn27RGGw==", "license": "Apache-2.0", "dependencies": {"lodash.camelcase": "^4.3.0", "long": "^5.0.0", "protobufjs": "^7.2.5", "yargs": "^17.7.2"}, "bin": {"proto-loader-gen-types": "build/bin/proto-loader-gen-types.js"}, "engines": {"node": ">=6"}}, "node_modules/@protobufjs/aspromise": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@protobufjs/aspromise/-/aspromise-1.1.2.tgz", "integrity": "sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/base64": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@protobufjs/base64/-/base64-1.1.2.tgz", "integrity": "sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/codegen": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/@protobufjs/codegen/-/codegen-2.0.4.tgz", "integrity": "sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/eventemitter": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz", "integrity": "sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/fetch": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/fetch/-/fetch-1.1.0.tgz", "integrity": "sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "node_modules/@protobufjs/float": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@protobufjs/float/-/float-1.0.2.tgz", "integrity": "sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/inquire": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/inquire/-/inquire-1.1.0.tgz", "integrity": "sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/path": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@protobufjs/path/-/path-1.1.2.tgz", "integrity": "sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/pool": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/pool/-/pool-1.1.0.tgz", "integrity": "sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/utf8": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/utf8/-/utf8-1.1.0.tgz", "integrity": "sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@types/node": {"version": "22.10.1", "resolved": "https://registry.npmjs.org/@types/node/-/node-22.10.1.tgz", "integrity": "sha512-qKgsUwfHZV2WCWLAnVP1JqnpE6Im6h3Y0+fYgMTasNQ7V++CBX5OT1as0g0f+OyubbFqhf6XVNIsmN4IIhEgGQ==", "license": "MIT", "dependencies": {"undici-types": "~6.20.0"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/cliui": {"version": "8.0.1", "resolved": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz", "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "license": "MIT"}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "license": "MIT"}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/faye-websocket": {"version": "0.11.4", "resolved": "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.4.tgz", "integrity": "sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==", "license": "Apache-2.0", "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/firebase": {"version": "11.0.2", "resolved": "https://registry.npmjs.org/firebase/-/firebase-11.0.2.tgz", "integrity": "sha512-w4T8BSJpzdZA25QRch5ahLsgB6uRvg1LEic4BaC5rTD1YygroI1AXp+W+rbMnr8d8EjfAv6t4k8doULIjc1P8Q==", "license": "Apache-2.0", "dependencies": {"@firebase/analytics": "0.10.10", "@firebase/analytics-compat": "0.2.16", "@firebase/app": "0.10.16", "@firebase/app-check": "0.8.10", "@firebase/app-check-compat": "0.3.17", "@firebase/app-compat": "0.2.46", "@firebase/app-types": "0.9.3", "@firebase/auth": "1.8.1", "@firebase/auth-compat": "0.5.16", "@firebase/data-connect": "0.1.2", "@firebase/database": "1.0.10", "@firebase/database-compat": "2.0.1", "@firebase/firestore": "4.7.5", "@firebase/firestore-compat": "0.3.40", "@firebase/functions": "0.11.10", "@firebase/functions-compat": "0.3.16", "@firebase/installations": "0.6.11", "@firebase/installations-compat": "0.2.11", "@firebase/messaging": "0.12.14", "@firebase/messaging-compat": "0.2.14", "@firebase/performance": "0.6.11", "@firebase/performance-compat": "0.2.11", "@firebase/remote-config": "0.4.11", "@firebase/remote-config-compat": "0.2.11", "@firebase/storage": "0.13.4", "@firebase/storage-compat": "0.3.14", "@firebase/util": "1.10.2", "@firebase/vertexai": "1.0.1"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/http-parser-js": {"version": "0.5.8", "resolved": "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.5.8.tgz", "integrity": "sha512-SGeBX54F94Wgu5RH3X5jsDtf4eHyRogWX1XGT3b4HuW3tQPM4AaBzoUji/4AAJNXCEOWZ5O0DgZmJw1947gD5Q==", "license": "MIT"}, "node_modules/idb": {"version": "7.1.1", "resolved": "https://registry.npmjs.org/idb/-/idb-7.1.1.tgz", "integrity": "sha512-gchesWBzyvGHRO9W8tzUWFDycow5gwjvFKfyV9FF32Y7F50yZMp7mP+T2mJIWFx49zicqyC4uefHM17o6xKIVQ==", "license": "ISC"}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/lodash.camelcase": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz", "integrity": "sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==", "license": "MIT"}, "node_modules/long": {"version": "5.2.3", "resolved": "https://registry.npmjs.org/long/-/long-5.2.3.tgz", "integrity": "sha512-lcHwpNoggQTObv5apGNCTdJrO69eHOZMi4BNC+rTLER8iHAqGrUVeLh/irVIM7zTw2bOXA8T6uNPeujwOLg/2Q==", "license": "Apache-2.0"}, "node_modules/protobufjs": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/protobufjs/-/protobufjs-7.4.0.tgz", "integrity": "sha512-mRUWCc3KUU4w1jU8sGxICXH/gNS94DvI1gxqDvBzhj1JpcsimQkYiOJfwsPUykUI5ZaspFbSgmBLER8IrQ3tqw==", "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/node": ">=13.7.0", "long": "^5.0.0"}, "engines": {"node": ">=12.0.0"}}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD"}, "node_modules/undici-types": {"version": "6.20.0", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-6.20.0.tgz", "integrity": "sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==", "license": "MIT"}, "node_modules/websocket-driver": {"version": "0.7.4", "resolved": "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz", "integrity": "sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==", "license": "Apache-2.0", "dependencies": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/websocket-extensions": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.4.tgz", "integrity": "sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==", "license": "Apache-2.0", "engines": {"node": ">=0.8.0"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yargs": {"version": "17.7.2", "resolved": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz", "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "license": "ISC", "engines": {"node": ">=12"}}}}