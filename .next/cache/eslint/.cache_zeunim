[{"/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/about/page.tsx": "1", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/api/analytics/route.ts": "2", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/api/email/route.ts": "3", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/layout.tsx": "4", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/page.tsx": "5", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/robots.ts": "6", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/sitemap.ts": "7", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/summit/page.tsx": "8", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Accordion.tsx": "9", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/AnalyticsProvider.tsx": "10", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/ContactSection.tsx": "11", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/EmailForm.tsx": "12", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/FeatureCard.tsx": "13", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Footer.tsx": "14", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/GoogleAnalytics.tsx": "15", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Header.tsx": "16", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/JoinWaitlistButton.tsx": "17", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/ProcessStep.tsx": "18", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/SEO.tsx": "19", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/StructuredData.tsx": "20", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/index.ts": "21", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/index.ts": "22", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useAccordion.ts": "23", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useAnalytics.ts": "24", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useEmailForm.ts": "25", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useIntersectionObserver.ts": "26", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useMobileMenu.ts": "27", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useScrollAnimation.ts": "28", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/lib/api.ts": "29", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/lib/emailjs.ts": "30", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/lib/firebase.ts": "31", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/api.ts": "32", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/firebase.ts": "33", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/index.ts": "34", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/ui.ts": "35", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/analytics.ts": "36", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/constants.ts": "37", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/helpers.ts": "38", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/seo.ts": "39"}, {"size": 11748, "mtime": 1753116137003, "results": "40", "hashOfConfig": "41"}, {"size": 1649, "mtime": 1753116501405, "results": "42", "hashOfConfig": "41"}, {"size": 1438, "mtime": 1753116487395, "results": "43", "hashOfConfig": "41"}, {"size": 991, "mtime": 1753117278507, "results": "44", "hashOfConfig": "41"}, {"size": 5157, "mtime": 1753117141246, "results": "45", "hashOfConfig": "41"}, {"size": 334, "mtime": 1753117017077, "results": "46", "hashOfConfig": "41"}, {"size": 632, "mtime": 1753117009362, "results": "47", "hashOfConfig": "41"}, {"size": 7144, "mtime": 1753116463475, "results": "48", "hashOfConfig": "41"}, {"size": 1349, "mtime": 1753116884623, "results": "49", "hashOfConfig": "41"}, {"size": 1615, "mtime": 1753117241237, "results": "50", "hashOfConfig": "41"}, {"size": 1465, "mtime": 1753115888162, "results": "51", "hashOfConfig": "41"}, {"size": 1852, "mtime": 1753116545075, "results": "52", "hashOfConfig": "41"}, {"size": 1920, "mtime": 1753115845389, "results": "53", "hashOfConfig": "41"}, {"size": 1813, "mtime": 1753117522879, "results": "54", "hashOfConfig": "41"}, {"size": 959, "mtime": 1753117252572, "results": "55", "hashOfConfig": "41"}, {"size": 4448, "mtime": 1753117509689, "results": "56", "hashOfConfig": "41"}, {"size": 856, "mtime": 1753116973451, "results": "57", "hashOfConfig": "41"}, {"size": 1001, "mtime": 1753115855335, "results": "58", "hashOfConfig": "41"}, {"size": 1784, "mtime": 1753115901331, "results": "59", "hashOfConfig": "41"}, {"size": 296, "mtime": 1753117535307, "results": "60", "hashOfConfig": "41"}, {"size": 733, "mtime": 1753117291676, "results": "61", "hashOfConfig": "41"}, {"size": 372, "mtime": 1753116987165, "results": "62", "hashOfConfig": "41"}, {"size": 2532, "mtime": 1753116826391, "results": "63", "hashOfConfig": "41"}, {"size": 3103, "mtime": 1753116844351, "results": "64", "hashOfConfig": "41"}, {"size": 1834, "mtime": 1753116530230, "results": "65", "hashOfConfig": "41"}, {"size": 1461, "mtime": 1753116858880, "results": "66", "hashOfConfig": "41"}, {"size": 1702, "mtime": 1753116810153, "results": "67", "hashOfConfig": "41"}, {"size": 2853, "mtime": 1753116014729, "results": "68", "hashOfConfig": "41"}, {"size": 1954, "mtime": 1753116516211, "results": "69", "hashOfConfig": "41"}, {"size": 709, "mtime": 1753115711389, "results": "70", "hashOfConfig": "41"}, {"size": 1593, "mtime": 1753117221946, "results": "71", "hashOfConfig": "41"}, {"size": 3091, "mtime": 1753116637393, "results": "72", "hashOfConfig": "41"}, {"size": 2641, "mtime": 1753116657263, "results": "73", "hashOfConfig": "41"}, {"size": 5239, "mtime": 1753116698136, "results": "74", "hashOfConfig": "41"}, {"size": 5765, "mtime": 1753116688623, "results": "75", "hashOfConfig": "41"}, {"size": 7262, "mtime": 1753117462593, "results": "76", "hashOfConfig": "41"}, {"size": 6508, "mtime": 1753115692846, "results": "77", "hashOfConfig": "41"}, {"size": 2128, "mtime": 1753115724845, "results": "78", "hashOfConfig": "41"}, {"size": 5044, "mtime": 1753117047662, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "txeug1", {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/about/page.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/api/analytics/route.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/api/email/route.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/layout.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/page.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/robots.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/sitemap.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/summit/page.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Accordion.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/AnalyticsProvider.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/ContactSection.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/EmailForm.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/FeatureCard.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Footer.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/GoogleAnalytics.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Header.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/JoinWaitlistButton.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/ProcessStep.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/SEO.tsx", ["197"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/StructuredData.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/index.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/index.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useAccordion.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useAnalytics.ts", ["198", "199", "200", "201", "202", "203", "204"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useEmailForm.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useIntersectionObserver.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useMobileMenu.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useScrollAnimation.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/lib/api.ts", ["205", "206", "207"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/lib/emailjs.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/lib/firebase.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/api.ts", ["208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/firebase.ts", ["231", "232", "233"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/index.ts", ["234", "235", "236", "237"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/ui.ts", ["238", "239", "240"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/analytics.ts", ["241", "242", "243", "244", "245", "246", "247", "248", "249", "250", "251", "252", "253", "254", "255", "256"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/constants.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/helpers.ts", ["257", "258", "259", "260"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/seo.ts", ["261"], [], {"ruleId": "262", "severity": 1, "message": "263", "line": 41, "column": 7, "nodeType": "264", "endLine": 44, "endColumn": 9}, {"ruleId": "265", "severity": 1, "message": "266", "line": 9, "column": 21, "nodeType": "267", "messageId": "268", "endLine": 9, "endColumn": 24, "suggestions": "269"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 38, "column": 37, "nodeType": "267", "messageId": "268", "endLine": 38, "endColumn": 40, "suggestions": "270"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 55, "column": 37, "nodeType": "267", "messageId": "268", "endLine": 55, "endColumn": 40, "suggestions": "271"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 70, "column": 37, "nodeType": "267", "messageId": "268", "endLine": 70, "endColumn": 40, "suggestions": "272"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 84, "column": 37, "nodeType": "267", "messageId": "268", "endLine": 84, "endColumn": 40, "suggestions": "273"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 97, "column": 37, "nodeType": "267", "messageId": "268", "endLine": 97, "endColumn": 40, "suggestions": "274"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 111, "column": 37, "nodeType": "267", "messageId": "268", "endLine": 111, "endColumn": 40, "suggestions": "275"}, {"ruleId": "276", "severity": 1, "message": "277", "line": 41, "column": 14, "nodeType": null, "messageId": "278", "endLine": 41, "endColumn": 19}, {"ruleId": "265", "severity": 1, "message": "266", "line": 49, "column": 68, "nodeType": "267", "messageId": "268", "endLine": 49, "endColumn": 71, "suggestions": "279"}, {"ruleId": "276", "severity": 1, "message": "280", "line": 66, "column": 28, "nodeType": null, "messageId": "278", "endLine": 66, "endColumn": 33}, {"ruleId": "265", "severity": 1, "message": "266", "line": 6, "column": 38, "nodeType": "267", "messageId": "268", "endLine": 6, "endColumn": 41, "suggestions": "281"}, {"ruleId": "282", "severity": 1, "message": "283", "line": 21, "column": 8, "nodeType": "284", "messageId": "285", "endLine": 35, "endColumn": 2}, {"ruleId": "282", "severity": 1, "message": "283", "line": 37, "column": 8, "nodeType": "284", "messageId": "285", "endLine": 55, "endColumn": 2}, {"ruleId": "265", "severity": 1, "message": "266", "line": 41, "column": 37, "nodeType": "267", "messageId": "268", "endLine": 41, "endColumn": 40, "suggestions": "286"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 70, "column": 29, "nodeType": "267", "messageId": "268", "endLine": 70, "endColumn": 32, "suggestions": "287"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 79, "column": 26, "nodeType": "267", "messageId": "268", "endLine": 79, "endColumn": 29, "suggestions": "288"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 79, "column": 34, "nodeType": "267", "messageId": "268", "endLine": 79, "endColumn": 37, "suggestions": "289"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 80, "column": 24, "nodeType": "267", "messageId": "268", "endLine": 80, "endColumn": 27, "suggestions": "290"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 80, "column": 32, "nodeType": "267", "messageId": "268", "endLine": 80, "endColumn": 35, "suggestions": "291"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 98, "column": 22, "nodeType": "267", "messageId": "268", "endLine": 98, "endColumn": 25, "suggestions": "292"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 108, "column": 11, "nodeType": "267", "messageId": "268", "endLine": 108, "endColumn": 14, "suggestions": "293"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 108, "column": 38, "nodeType": "267", "messageId": "268", "endLine": 108, "endColumn": 41, "suggestions": "294"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 109, "column": 12, "nodeType": "267", "messageId": "268", "endLine": 109, "endColumn": 15, "suggestions": "295"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 109, "column": 37, "nodeType": "267", "messageId": "268", "endLine": 109, "endColumn": 40, "suggestions": "296"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 109, "column": 51, "nodeType": "267", "messageId": "268", "endLine": 109, "endColumn": 54, "suggestions": "297"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 110, "column": 11, "nodeType": "267", "messageId": "268", "endLine": 110, "endColumn": 14, "suggestions": "298"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 110, "column": 36, "nodeType": "267", "messageId": "268", "endLine": 110, "endColumn": 39, "suggestions": "299"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 110, "column": 50, "nodeType": "267", "messageId": "268", "endLine": 110, "endColumn": 53, "suggestions": "300"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 111, "column": 14, "nodeType": "267", "messageId": "268", "endLine": 111, "endColumn": 17, "suggestions": "301"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 111, "column": 41, "nodeType": "267", "messageId": "268", "endLine": 111, "endColumn": 44, "suggestions": "302"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 112, "column": 13, "nodeType": "267", "messageId": "268", "endLine": 112, "endColumn": 16, "suggestions": "303"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 112, "column": 38, "nodeType": "267", "messageId": "268", "endLine": 112, "endColumn": 41, "suggestions": "304"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 112, "column": 52, "nodeType": "267", "messageId": "268", "endLine": 112, "endColumn": 55, "suggestions": "305"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 78, "column": 30, "nodeType": "267", "messageId": "268", "endLine": 78, "endColumn": 33, "suggestions": "306"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 85, "column": 36, "nodeType": "267", "messageId": "268", "endLine": 85, "endColumn": 39, "suggestions": "307"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 103, "column": 16, "nodeType": "267", "messageId": "268", "endLine": 103, "endColumn": 19, "suggestions": "308"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 82, "column": 34, "nodeType": "267", "messageId": "268", "endLine": 82, "endColumn": 37, "suggestions": "309"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 100, "column": 35, "nodeType": "267", "messageId": "268", "endLine": 100, "endColumn": 38, "suggestions": "310"}, {"ruleId": "311", "severity": 1, "message": "312", "line": 250, "column": 39, "nodeType": "313", "messageId": "314", "endLine": 250, "endColumn": 41, "suggestions": "315"}, {"ruleId": "311", "severity": 1, "message": "312", "line": 251, "column": 40, "nodeType": "313", "messageId": "314", "endLine": 251, "endColumn": 42, "suggestions": "316"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 182, "column": 35, "nodeType": "267", "messageId": "268", "endLine": 182, "endColumn": 38, "suggestions": "317"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 183, "column": 22, "nodeType": "267", "messageId": "268", "endLine": 183, "endColumn": 25, "suggestions": "318"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 184, "column": 34, "nodeType": "267", "messageId": "268", "endLine": 184, "endColumn": 37, "suggestions": "319"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 6, "column": 21, "nodeType": "267", "messageId": "268", "endLine": 6, "endColumn": 24, "suggestions": "320"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 73, "column": 32, "nodeType": "267", "messageId": "268", "endLine": 73, "endColumn": 35, "suggestions": "321"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 88, "column": 67, "nodeType": "267", "messageId": "268", "endLine": 88, "endColumn": 70, "suggestions": "322"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 118, "column": 36, "nodeType": "267", "messageId": "268", "endLine": 118, "endColumn": 39, "suggestions": "323"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 132, "column": 36, "nodeType": "267", "messageId": "268", "endLine": 132, "endColumn": 39, "suggestions": "324"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 146, "column": 36, "nodeType": "267", "messageId": "268", "endLine": 146, "endColumn": 39, "suggestions": "325"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 159, "column": 36, "nodeType": "267", "messageId": "268", "endLine": 159, "endColumn": 39, "suggestions": "326"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 172, "column": 36, "nodeType": "267", "messageId": "268", "endLine": 172, "endColumn": 39, "suggestions": "327"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 200, "column": 36, "nodeType": "267", "messageId": "268", "endLine": 200, "endColumn": 39, "suggestions": "328"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 216, "column": 36, "nodeType": "267", "messageId": "268", "endLine": 216, "endColumn": 39, "suggestions": "329"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 231, "column": 36, "nodeType": "267", "messageId": "268", "endLine": 231, "endColumn": 39, "suggestions": "330"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 248, "column": 30, "nodeType": "267", "messageId": "268", "endLine": 248, "endColumn": 33, "suggestions": "331"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 260, "column": 35, "nodeType": "267", "messageId": "268", "endLine": 260, "endColumn": 38, "suggestions": "332"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 267, "column": 35, "nodeType": "267", "messageId": "268", "endLine": 267, "endColumn": 38, "suggestions": "333"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 273, "column": 35, "nodeType": "267", "messageId": "268", "endLine": 273, "endColumn": 38, "suggestions": "334"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 280, "column": 35, "nodeType": "267", "messageId": "268", "endLine": 280, "endColumn": 38, "suggestions": "335"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 37, "column": 46, "nodeType": "267", "messageId": "268", "endLine": 37, "endColumn": 49, "suggestions": "336"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 37, "column": 56, "nodeType": "267", "messageId": "268", "endLine": 37, "endColumn": 59, "suggestions": "337"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 49, "column": 46, "nodeType": "267", "messageId": "268", "endLine": 49, "endColumn": 49, "suggestions": "338"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 49, "column": 56, "nodeType": "267", "messageId": "268", "endLine": 49, "endColumn": 59, "suggestions": "339"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 13, "column": 20, "nodeType": "267", "messageId": "268", "endLine": 13, "endColumn": 23, "suggestions": "340"}, "@next/next/no-page-custom-font", "Custom fonts not added in `pages/_document.js` will only load for a single page. This is discouraged. See: https://nextjs.org/docs/messages/no-page-custom-font", "JSXOpeningElement", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["341", "342"], ["343", "344"], ["345", "346"], ["347", "348"], ["349", "350"], ["351", "352"], ["353", "354"], "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", ["355", "356"], "'email' is defined but never used.", ["357", "358"], "@typescript-eslint/no-namespace", "ES2015 module syntax is preferred over namespaces.", "TSModuleDeclaration", "moduleSyntaxIsPreferred", ["359", "360"], ["361", "362"], ["363", "364"], ["365", "366"], ["367", "368"], ["369", "370"], ["371", "372"], ["373", "374"], ["375", "376"], ["377", "378"], ["379", "380"], ["381", "382"], ["383", "384"], ["385", "386"], ["387", "388"], ["389", "390"], ["391", "392"], ["393", "394"], ["395", "396"], ["397", "398"], ["399", "400"], ["401", "402"], ["403", "404"], ["405", "406"], ["407", "408"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["409", "410"], ["411", "412"], ["413", "414"], ["415", "416"], ["417", "418"], ["419", "420"], ["421", "422"], ["423", "424"], ["425", "426"], ["427", "428"], ["429", "430"], ["431", "432"], ["433", "434"], ["435", "436"], ["437", "438"], ["439", "440"], ["441", "442"], ["443", "444"], ["445", "446"], ["447", "448"], ["449", "450"], ["451", "452"], ["453", "454"], ["455", "456"], ["457", "458"], ["459", "460"], {"messageId": "461", "fix": "462", "desc": "463"}, {"messageId": "464", "fix": "465", "desc": "466"}, {"messageId": "461", "fix": "467", "desc": "463"}, {"messageId": "464", "fix": "468", "desc": "466"}, {"messageId": "461", "fix": "469", "desc": "463"}, {"messageId": "464", "fix": "470", "desc": "466"}, {"messageId": "461", "fix": "471", "desc": "463"}, {"messageId": "464", "fix": "472", "desc": "466"}, {"messageId": "461", "fix": "473", "desc": "463"}, {"messageId": "464", "fix": "474", "desc": "466"}, {"messageId": "461", "fix": "475", "desc": "463"}, {"messageId": "464", "fix": "476", "desc": "466"}, {"messageId": "461", "fix": "477", "desc": "463"}, {"messageId": "464", "fix": "478", "desc": "466"}, {"messageId": "461", "fix": "479", "desc": "463"}, {"messageId": "464", "fix": "480", "desc": "466"}, {"messageId": "461", "fix": "481", "desc": "463"}, {"messageId": "464", "fix": "482", "desc": "466"}, {"messageId": "461", "fix": "483", "desc": "463"}, {"messageId": "464", "fix": "484", "desc": "466"}, {"messageId": "461", "fix": "485", "desc": "463"}, {"messageId": "464", "fix": "486", "desc": "466"}, {"messageId": "461", "fix": "487", "desc": "463"}, {"messageId": "464", "fix": "488", "desc": "466"}, {"messageId": "461", "fix": "489", "desc": "463"}, {"messageId": "464", "fix": "490", "desc": "466"}, {"messageId": "461", "fix": "491", "desc": "463"}, {"messageId": "464", "fix": "492", "desc": "466"}, {"messageId": "461", "fix": "493", "desc": "463"}, {"messageId": "464", "fix": "494", "desc": "466"}, {"messageId": "461", "fix": "495", "desc": "463"}, {"messageId": "464", "fix": "496", "desc": "466"}, {"messageId": "461", "fix": "497", "desc": "463"}, {"messageId": "464", "fix": "498", "desc": "466"}, {"messageId": "461", "fix": "499", "desc": "463"}, {"messageId": "464", "fix": "500", "desc": "466"}, {"messageId": "461", "fix": "501", "desc": "463"}, {"messageId": "464", "fix": "502", "desc": "466"}, {"messageId": "461", "fix": "503", "desc": "463"}, {"messageId": "464", "fix": "504", "desc": "466"}, {"messageId": "461", "fix": "505", "desc": "463"}, {"messageId": "464", "fix": "506", "desc": "466"}, {"messageId": "461", "fix": "507", "desc": "463"}, {"messageId": "464", "fix": "508", "desc": "466"}, {"messageId": "461", "fix": "509", "desc": "463"}, {"messageId": "464", "fix": "510", "desc": "466"}, {"messageId": "461", "fix": "511", "desc": "463"}, {"messageId": "464", "fix": "512", "desc": "466"}, {"messageId": "461", "fix": "513", "desc": "463"}, {"messageId": "464", "fix": "514", "desc": "466"}, {"messageId": "461", "fix": "515", "desc": "463"}, {"messageId": "464", "fix": "516", "desc": "466"}, {"messageId": "461", "fix": "517", "desc": "463"}, {"messageId": "464", "fix": "518", "desc": "466"}, {"messageId": "461", "fix": "519", "desc": "463"}, {"messageId": "464", "fix": "520", "desc": "466"}, {"messageId": "461", "fix": "521", "desc": "463"}, {"messageId": "464", "fix": "522", "desc": "466"}, {"messageId": "461", "fix": "523", "desc": "463"}, {"messageId": "464", "fix": "524", "desc": "466"}, {"messageId": "461", "fix": "525", "desc": "463"}, {"messageId": "464", "fix": "526", "desc": "466"}, {"messageId": "461", "fix": "527", "desc": "463"}, {"messageId": "464", "fix": "528", "desc": "466"}, {"messageId": "461", "fix": "529", "desc": "463"}, {"messageId": "464", "fix": "530", "desc": "466"}, {"messageId": "461", "fix": "531", "desc": "463"}, {"messageId": "464", "fix": "532", "desc": "466"}, {"messageId": "533", "data": "534", "fix": "535", "desc": "536"}, {"messageId": "533", "data": "537", "fix": "538", "desc": "539"}, {"messageId": "533", "data": "540", "fix": "541", "desc": "536"}, {"messageId": "533", "data": "542", "fix": "543", "desc": "539"}, {"messageId": "461", "fix": "544", "desc": "463"}, {"messageId": "464", "fix": "545", "desc": "466"}, {"messageId": "461", "fix": "546", "desc": "463"}, {"messageId": "464", "fix": "547", "desc": "466"}, {"messageId": "461", "fix": "548", "desc": "463"}, {"messageId": "464", "fix": "549", "desc": "466"}, {"messageId": "461", "fix": "550", "desc": "463"}, {"messageId": "464", "fix": "551", "desc": "466"}, {"messageId": "461", "fix": "552", "desc": "463"}, {"messageId": "464", "fix": "553", "desc": "466"}, {"messageId": "461", "fix": "554", "desc": "463"}, {"messageId": "464", "fix": "555", "desc": "466"}, {"messageId": "461", "fix": "556", "desc": "463"}, {"messageId": "464", "fix": "557", "desc": "466"}, {"messageId": "461", "fix": "558", "desc": "463"}, {"messageId": "464", "fix": "559", "desc": "466"}, {"messageId": "461", "fix": "560", "desc": "463"}, {"messageId": "464", "fix": "561", "desc": "466"}, {"messageId": "461", "fix": "562", "desc": "463"}, {"messageId": "464", "fix": "563", "desc": "466"}, {"messageId": "461", "fix": "564", "desc": "463"}, {"messageId": "464", "fix": "565", "desc": "466"}, {"messageId": "461", "fix": "566", "desc": "463"}, {"messageId": "464", "fix": "567", "desc": "466"}, {"messageId": "461", "fix": "568", "desc": "463"}, {"messageId": "464", "fix": "569", "desc": "466"}, {"messageId": "461", "fix": "570", "desc": "463"}, {"messageId": "464", "fix": "571", "desc": "466"}, {"messageId": "461", "fix": "572", "desc": "463"}, {"messageId": "464", "fix": "573", "desc": "466"}, {"messageId": "461", "fix": "574", "desc": "463"}, {"messageId": "464", "fix": "575", "desc": "466"}, {"messageId": "461", "fix": "576", "desc": "463"}, {"messageId": "464", "fix": "577", "desc": "466"}, {"messageId": "461", "fix": "578", "desc": "463"}, {"messageId": "464", "fix": "579", "desc": "466"}, {"messageId": "461", "fix": "580", "desc": "463"}, {"messageId": "464", "fix": "581", "desc": "466"}, {"messageId": "461", "fix": "582", "desc": "463"}, {"messageId": "464", "fix": "583", "desc": "466"}, {"messageId": "461", "fix": "584", "desc": "463"}, {"messageId": "464", "fix": "585", "desc": "466"}, {"messageId": "461", "fix": "586", "desc": "463"}, {"messageId": "464", "fix": "587", "desc": "466"}, {"messageId": "461", "fix": "588", "desc": "463"}, {"messageId": "464", "fix": "589", "desc": "466"}, {"messageId": "461", "fix": "590", "desc": "463"}, {"messageId": "464", "fix": "591", "desc": "466"}, "suggestUnknown", {"range": "592", "text": "593"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "594", "text": "595"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "596", "text": "593"}, {"range": "597", "text": "595"}, {"range": "598", "text": "593"}, {"range": "599", "text": "595"}, {"range": "600", "text": "593"}, {"range": "601", "text": "595"}, {"range": "602", "text": "593"}, {"range": "603", "text": "595"}, {"range": "604", "text": "593"}, {"range": "605", "text": "595"}, {"range": "606", "text": "593"}, {"range": "607", "text": "595"}, {"range": "608", "text": "593"}, {"range": "609", "text": "595"}, {"range": "610", "text": "593"}, {"range": "611", "text": "595"}, {"range": "612", "text": "593"}, {"range": "613", "text": "595"}, {"range": "614", "text": "593"}, {"range": "615", "text": "595"}, {"range": "616", "text": "593"}, {"range": "617", "text": "595"}, {"range": "618", "text": "593"}, {"range": "619", "text": "595"}, {"range": "620", "text": "593"}, {"range": "621", "text": "595"}, {"range": "622", "text": "593"}, {"range": "623", "text": "595"}, {"range": "624", "text": "593"}, {"range": "625", "text": "595"}, {"range": "626", "text": "593"}, {"range": "627", "text": "595"}, {"range": "628", "text": "593"}, {"range": "629", "text": "595"}, {"range": "630", "text": "593"}, {"range": "631", "text": "595"}, {"range": "632", "text": "593"}, {"range": "633", "text": "595"}, {"range": "634", "text": "593"}, {"range": "635", "text": "595"}, {"range": "636", "text": "593"}, {"range": "637", "text": "595"}, {"range": "638", "text": "593"}, {"range": "639", "text": "595"}, {"range": "640", "text": "593"}, {"range": "641", "text": "595"}, {"range": "642", "text": "593"}, {"range": "643", "text": "595"}, {"range": "644", "text": "593"}, {"range": "645", "text": "595"}, {"range": "646", "text": "593"}, {"range": "647", "text": "595"}, {"range": "648", "text": "593"}, {"range": "649", "text": "595"}, {"range": "650", "text": "593"}, {"range": "651", "text": "595"}, {"range": "652", "text": "593"}, {"range": "653", "text": "595"}, {"range": "654", "text": "593"}, {"range": "655", "text": "595"}, {"range": "656", "text": "593"}, {"range": "657", "text": "595"}, {"range": "658", "text": "593"}, {"range": "659", "text": "595"}, {"range": "660", "text": "593"}, {"range": "661", "text": "595"}, "replaceEmptyObjectType", {"replacement": "662"}, {"range": "663", "text": "662"}, "Replace `{}` with `object`.", {"replacement": "593"}, {"range": "664", "text": "593"}, "Replace `{}` with `unknown`.", {"replacement": "662"}, {"range": "665", "text": "662"}, {"replacement": "593"}, {"range": "666", "text": "593"}, {"range": "667", "text": "593"}, {"range": "668", "text": "595"}, {"range": "669", "text": "593"}, {"range": "670", "text": "595"}, {"range": "671", "text": "593"}, {"range": "672", "text": "595"}, {"range": "673", "text": "593"}, {"range": "674", "text": "595"}, {"range": "675", "text": "593"}, {"range": "676", "text": "595"}, {"range": "677", "text": "593"}, {"range": "678", "text": "595"}, {"range": "679", "text": "593"}, {"range": "680", "text": "595"}, {"range": "681", "text": "593"}, {"range": "682", "text": "595"}, {"range": "683", "text": "593"}, {"range": "684", "text": "595"}, {"range": "685", "text": "593"}, {"range": "686", "text": "595"}, {"range": "687", "text": "593"}, {"range": "688", "text": "595"}, {"range": "689", "text": "593"}, {"range": "690", "text": "595"}, {"range": "691", "text": "593"}, {"range": "692", "text": "595"}, {"range": "693", "text": "593"}, {"range": "694", "text": "595"}, {"range": "695", "text": "593"}, {"range": "696", "text": "595"}, {"range": "697", "text": "593"}, {"range": "698", "text": "595"}, {"range": "699", "text": "593"}, {"range": "700", "text": "595"}, {"range": "701", "text": "593"}, {"range": "702", "text": "595"}, {"range": "703", "text": "593"}, {"range": "704", "text": "595"}, {"range": "705", "text": "593"}, {"range": "706", "text": "595"}, {"range": "707", "text": "593"}, {"range": "708", "text": "595"}, {"range": "709", "text": "593"}, {"range": "710", "text": "595"}, {"range": "711", "text": "593"}, {"range": "712", "text": "595"}, {"range": "713", "text": "593"}, {"range": "714", "text": "595"}, [210, 213], "unknown", [210, 213], "never", [914, 917], [914, 917], [1331, 1334], [1331, 1334], [1721, 1724], [1721, 1724], [2073, 2076], [2073, 2076], [2413, 2416], [2413, 2416], [2774, 2777], [2774, 2777], [1068, 1071], [1068, 1071], [158, 161], [158, 161], [784, 787], [784, 787], [1315, 1318], [1315, 1318], [1572, 1575], [1572, 1575], [1580, 1583], [1580, 1583], [1608, 1611], [1608, 1611], [1616, 1619], [1616, 1619], [2048, 2051], [2048, 2051], [2254, 2257], [2254, 2257], [2281, 2284], [2281, 2284], [2323, 2326], [2323, 2326], [2348, 2351], [2348, 2351], [2362, 2365], [2362, 2365], [2403, 2406], [2403, 2406], [2428, 2431], [2428, 2431], [2442, 2445], [2442, 2445], [2486, 2489], [2486, 2489], [2513, 2516], [2513, 2516], [2556, 2559], [2556, 2559], [2581, 2584], [2581, 2584], [2595, 2598], [2595, 2598], [1640, 1643], [1640, 1643], [1784, 1787], [1784, 1787], [2156, 2159], [2156, 2159], [1410, 1413], [1410, 1413], [1709, 1712], [1709, 1712], "object", [4838, 4840], [4838, 4840], [4929, 4931], [4929, 4931], [4372, 4375], [4372, 4375], [4424, 4427], [4424, 4427], [4462, 4465], [4462, 4465], [157, 160], [157, 160], [1891, 1894], [1891, 1894], [2441, 2444], [2441, 2444], [3200, 3203], [3200, 3203], [3530, 3533], [3530, 3533], [3836, 3839], [3836, 3839], [4148, 4151], [4148, 4151], [4471, 4474], [4471, 4474], [5141, 5144], [5141, 5144], [5499, 5502], [5499, 5502], [5854, 5857], [5854, 5857], [6270, 6273], [6270, 6273], [6581, 6584], [6581, 6584], [6783, 6786], [6783, 6786], [6976, 6979], [6976, 6979], [7182, 7185], [7182, 7185], [858, 861], [858, 861], [868, 871], [868, 871], [1168, 1171], [1168, 1171], [1178, 1181], [1178, 1181], [284, 287], [284, 287]]