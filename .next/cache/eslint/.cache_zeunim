[{"/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/about/page.tsx": "1", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/api/analytics/route.ts": "2", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/api/email/route.ts": "3", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/layout.tsx": "4", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/page.tsx": "5", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/robots.ts": "6", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/sitemap.ts": "7", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/summit/page.tsx": "8", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Accordion.tsx": "9", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/AnalyticsProvider.tsx": "10", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/ContactSection.tsx": "11", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/EmailForm.tsx": "12", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/FeatureCard.tsx": "13", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Footer.tsx": "14", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/GoogleAnalytics.tsx": "15", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Header.tsx": "16", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/JoinWaitlistButton.tsx": "17", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/ProcessStep.tsx": "18", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/SEO.tsx": "19", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/StructuredData.tsx": "20", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/index.ts": "21", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/index.ts": "22", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useAccordion.ts": "23", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useAnalytics.ts": "24", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useEmailForm.ts": "25", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useIntersectionObserver.ts": "26", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useMobileMenu.ts": "27", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useScrollAnimation.ts": "28", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/lib/api.ts": "29", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/lib/emailjs.ts": "30", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/api.ts": "31", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/index.ts": "32", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/ui.ts": "33", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/analytics.ts": "34", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/constants.ts": "35", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/helpers.ts": "36", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/seo.ts": "37"}, {"size": 11748, "mtime": 1753116137003, "results": "38", "hashOfConfig": "39"}, {"size": 1649, "mtime": 1753116501405, "results": "40", "hashOfConfig": "39"}, {"size": 1438, "mtime": 1753116487395, "results": "41", "hashOfConfig": "39"}, {"size": 991, "mtime": 1753117278507, "results": "42", "hashOfConfig": "39"}, {"size": 5157, "mtime": 1753117141246, "results": "43", "hashOfConfig": "39"}, {"size": 334, "mtime": 1753117017077, "results": "44", "hashOfConfig": "39"}, {"size": 632, "mtime": 1753117009362, "results": "45", "hashOfConfig": "39"}, {"size": 7144, "mtime": 1753116463475, "results": "46", "hashOfConfig": "39"}, {"size": 1349, "mtime": 1753116884623, "results": "47", "hashOfConfig": "39"}, {"size": 1226, "mtime": 1753118079092, "results": "48", "hashOfConfig": "39"}, {"size": 1465, "mtime": 1753115888162, "results": "49", "hashOfConfig": "39"}, {"size": 1852, "mtime": 1753116545075, "results": "50", "hashOfConfig": "39"}, {"size": 1920, "mtime": 1753115845389, "results": "51", "hashOfConfig": "39"}, {"size": 1813, "mtime": 1753117522879, "results": "52", "hashOfConfig": "39"}, {"size": 959, "mtime": 1753117252572, "results": "53", "hashOfConfig": "39"}, {"size": 4448, "mtime": 1753117509689, "results": "54", "hashOfConfig": "39"}, {"size": 856, "mtime": 1753116973451, "results": "55", "hashOfConfig": "39"}, {"size": 1001, "mtime": 1753115855335, "results": "56", "hashOfConfig": "39"}, {"size": 1784, "mtime": 1753115901331, "results": "57", "hashOfConfig": "39"}, {"size": 296, "mtime": 1753117535307, "results": "58", "hashOfConfig": "39"}, {"size": 733, "mtime": 1753117291676, "results": "59", "hashOfConfig": "39"}, {"size": 372, "mtime": 1753116987165, "results": "60", "hashOfConfig": "39"}, {"size": 2532, "mtime": 1753116826391, "results": "61", "hashOfConfig": "39"}, {"size": 3103, "mtime": 1753116844351, "results": "62", "hashOfConfig": "39"}, {"size": 1834, "mtime": 1753116530230, "results": "63", "hashOfConfig": "39"}, {"size": 1461, "mtime": 1753116858880, "results": "64", "hashOfConfig": "39"}, {"size": 1702, "mtime": 1753116810153, "results": "65", "hashOfConfig": "39"}, {"size": 2853, "mtime": 1753116014729, "results": "66", "hashOfConfig": "39"}, {"size": 1954, "mtime": 1753116516211, "results": "67", "hashOfConfig": "39"}, {"size": 709, "mtime": 1753115711389, "results": "68", "hashOfConfig": "39"}, {"size": 3091, "mtime": 1753116637393, "results": "69", "hashOfConfig": "39"}, {"size": 5019, "mtime": 1753118056484, "results": "70", "hashOfConfig": "39"}, {"size": 5765, "mtime": 1753116688623, "results": "71", "hashOfConfig": "39"}, {"size": 6216, "mtime": 1753118145789, "results": "72", "hashOfConfig": "39"}, {"size": 6508, "mtime": 1753115692846, "results": "73", "hashOfConfig": "39"}, {"size": 2128, "mtime": 1753115724845, "results": "74", "hashOfConfig": "39"}, {"size": 5044, "mtime": 1753117047662, "results": "75", "hashOfConfig": "39"}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "txeug1", {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/about/page.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/api/analytics/route.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/api/email/route.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/layout.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/page.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/robots.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/sitemap.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/summit/page.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Accordion.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/AnalyticsProvider.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/ContactSection.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/EmailForm.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/FeatureCard.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Footer.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/GoogleAnalytics.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Header.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/JoinWaitlistButton.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/ProcessStep.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/SEO.tsx", ["187"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/StructuredData.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/index.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/index.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useAccordion.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useAnalytics.ts", ["188", "189", "190", "191", "192", "193", "194"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useEmailForm.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useIntersectionObserver.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useMobileMenu.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useScrollAnimation.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/lib/api.ts", ["195", "196", "197"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/lib/emailjs.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/api.ts", ["198", "199", "200", "201", "202", "203", "204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "220"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/index.ts", ["221", "222", "223", "224"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/ui.ts", ["225", "226", "227"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/analytics.ts", ["228", "229", "230", "231", "232", "233", "234", "235", "236", "237", "238", "239", "240", "241", "242"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/constants.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/helpers.ts", ["243", "244", "245", "246"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/seo.ts", ["247"], [], {"ruleId": "248", "severity": 1, "message": "249", "line": 41, "column": 7, "nodeType": "250", "endLine": 44, "endColumn": 9}, {"ruleId": "251", "severity": 1, "message": "252", "line": 9, "column": 21, "nodeType": "253", "messageId": "254", "endLine": 9, "endColumn": 24, "suggestions": "255"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 38, "column": 37, "nodeType": "253", "messageId": "254", "endLine": 38, "endColumn": 40, "suggestions": "256"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 55, "column": 37, "nodeType": "253", "messageId": "254", "endLine": 55, "endColumn": 40, "suggestions": "257"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 70, "column": 37, "nodeType": "253", "messageId": "254", "endLine": 70, "endColumn": 40, "suggestions": "258"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 84, "column": 37, "nodeType": "253", "messageId": "254", "endLine": 84, "endColumn": 40, "suggestions": "259"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 97, "column": 37, "nodeType": "253", "messageId": "254", "endLine": 97, "endColumn": 40, "suggestions": "260"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 111, "column": 37, "nodeType": "253", "messageId": "254", "endLine": 111, "endColumn": 40, "suggestions": "261"}, {"ruleId": "262", "severity": 1, "message": "263", "line": 41, "column": 14, "nodeType": null, "messageId": "264", "endLine": 41, "endColumn": 19}, {"ruleId": "251", "severity": 1, "message": "252", "line": 49, "column": 68, "nodeType": "253", "messageId": "254", "endLine": 49, "endColumn": 71, "suggestions": "265"}, {"ruleId": "262", "severity": 1, "message": "266", "line": 66, "column": 28, "nodeType": null, "messageId": "264", "endLine": 66, "endColumn": 33}, {"ruleId": "251", "severity": 1, "message": "252", "line": 6, "column": 38, "nodeType": "253", "messageId": "254", "endLine": 6, "endColumn": 41, "suggestions": "267"}, {"ruleId": "268", "severity": 1, "message": "269", "line": 21, "column": 8, "nodeType": "270", "messageId": "271", "endLine": 35, "endColumn": 2}, {"ruleId": "268", "severity": 1, "message": "269", "line": 37, "column": 8, "nodeType": "270", "messageId": "271", "endLine": 55, "endColumn": 2}, {"ruleId": "251", "severity": 1, "message": "252", "line": 41, "column": 37, "nodeType": "253", "messageId": "254", "endLine": 41, "endColumn": 40, "suggestions": "272"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 70, "column": 29, "nodeType": "253", "messageId": "254", "endLine": 70, "endColumn": 32, "suggestions": "273"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 79, "column": 26, "nodeType": "253", "messageId": "254", "endLine": 79, "endColumn": 29, "suggestions": "274"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 79, "column": 34, "nodeType": "253", "messageId": "254", "endLine": 79, "endColumn": 37, "suggestions": "275"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 80, "column": 24, "nodeType": "253", "messageId": "254", "endLine": 80, "endColumn": 27, "suggestions": "276"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 80, "column": 32, "nodeType": "253", "messageId": "254", "endLine": 80, "endColumn": 35, "suggestions": "277"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 98, "column": 22, "nodeType": "253", "messageId": "254", "endLine": 98, "endColumn": 25, "suggestions": "278"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 108, "column": 11, "nodeType": "253", "messageId": "254", "endLine": 108, "endColumn": 14, "suggestions": "279"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 108, "column": 38, "nodeType": "253", "messageId": "254", "endLine": 108, "endColumn": 41, "suggestions": "280"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 109, "column": 12, "nodeType": "253", "messageId": "254", "endLine": 109, "endColumn": 15, "suggestions": "281"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 109, "column": 37, "nodeType": "253", "messageId": "254", "endLine": 109, "endColumn": 40, "suggestions": "282"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 109, "column": 51, "nodeType": "253", "messageId": "254", "endLine": 109, "endColumn": 54, "suggestions": "283"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 110, "column": 11, "nodeType": "253", "messageId": "254", "endLine": 110, "endColumn": 14, "suggestions": "284"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 110, "column": 36, "nodeType": "253", "messageId": "254", "endLine": 110, "endColumn": 39, "suggestions": "285"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 110, "column": 50, "nodeType": "253", "messageId": "254", "endLine": 110, "endColumn": 53, "suggestions": "286"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 111, "column": 14, "nodeType": "253", "messageId": "254", "endLine": 111, "endColumn": 17, "suggestions": "287"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 111, "column": 41, "nodeType": "253", "messageId": "254", "endLine": 111, "endColumn": 44, "suggestions": "288"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 112, "column": 13, "nodeType": "253", "messageId": "254", "endLine": 112, "endColumn": 16, "suggestions": "289"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 112, "column": 38, "nodeType": "253", "messageId": "254", "endLine": 112, "endColumn": 41, "suggestions": "290"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 112, "column": 52, "nodeType": "253", "messageId": "254", "endLine": 112, "endColumn": 55, "suggestions": "291"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 73, "column": 34, "nodeType": "253", "messageId": "254", "endLine": 73, "endColumn": 37, "suggestions": "292"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 91, "column": 35, "nodeType": "253", "messageId": "254", "endLine": 91, "endColumn": 38, "suggestions": "293"}, {"ruleId": "294", "severity": 1, "message": "295", "line": 241, "column": 39, "nodeType": "296", "messageId": "297", "endLine": 241, "endColumn": 41, "suggestions": "298"}, {"ruleId": "294", "severity": 1, "message": "295", "line": 242, "column": 40, "nodeType": "296", "messageId": "297", "endLine": 242, "endColumn": 42, "suggestions": "299"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 182, "column": 35, "nodeType": "253", "messageId": "254", "endLine": 182, "endColumn": 38, "suggestions": "300"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 183, "column": 22, "nodeType": "253", "messageId": "254", "endLine": 183, "endColumn": 25, "suggestions": "301"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 184, "column": 34, "nodeType": "253", "messageId": "254", "endLine": 184, "endColumn": 37, "suggestions": "302"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 5, "column": 21, "nodeType": "253", "messageId": "254", "endLine": 5, "endColumn": 24, "suggestions": "303"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 54, "column": 32, "nodeType": "253", "messageId": "254", "endLine": 54, "endColumn": 35, "suggestions": "304"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 91, "column": 36, "nodeType": "253", "messageId": "254", "endLine": 91, "endColumn": 39, "suggestions": "305"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 105, "column": 36, "nodeType": "253", "messageId": "254", "endLine": 105, "endColumn": 39, "suggestions": "306"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 119, "column": 36, "nodeType": "253", "messageId": "254", "endLine": 119, "endColumn": 39, "suggestions": "307"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 132, "column": 36, "nodeType": "253", "messageId": "254", "endLine": 132, "endColumn": 39, "suggestions": "308"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 145, "column": 36, "nodeType": "253", "messageId": "254", "endLine": 145, "endColumn": 39, "suggestions": "309"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 173, "column": 36, "nodeType": "253", "messageId": "254", "endLine": 173, "endColumn": 39, "suggestions": "310"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 189, "column": 36, "nodeType": "253", "messageId": "254", "endLine": 189, "endColumn": 39, "suggestions": "311"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 204, "column": 36, "nodeType": "253", "messageId": "254", "endLine": 204, "endColumn": 39, "suggestions": "312"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 221, "column": 30, "nodeType": "253", "messageId": "254", "endLine": 221, "endColumn": 33, "suggestions": "313"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 233, "column": 35, "nodeType": "253", "messageId": "254", "endLine": 233, "endColumn": 38, "suggestions": "314"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 240, "column": 35, "nodeType": "253", "messageId": "254", "endLine": 240, "endColumn": 38, "suggestions": "315"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 246, "column": 35, "nodeType": "253", "messageId": "254", "endLine": 246, "endColumn": 38, "suggestions": "316"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 253, "column": 35, "nodeType": "253", "messageId": "254", "endLine": 253, "endColumn": 38, "suggestions": "317"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 37, "column": 46, "nodeType": "253", "messageId": "254", "endLine": 37, "endColumn": 49, "suggestions": "318"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 37, "column": 56, "nodeType": "253", "messageId": "254", "endLine": 37, "endColumn": 59, "suggestions": "319"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 49, "column": 46, "nodeType": "253", "messageId": "254", "endLine": 49, "endColumn": 49, "suggestions": "320"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 49, "column": 56, "nodeType": "253", "messageId": "254", "endLine": 49, "endColumn": 59, "suggestions": "321"}, {"ruleId": "251", "severity": 1, "message": "252", "line": 13, "column": 20, "nodeType": "253", "messageId": "254", "endLine": 13, "endColumn": 23, "suggestions": "322"}, "@next/next/no-page-custom-font", "Custom fonts not added in `pages/_document.js` will only load for a single page. This is discouraged. See: https://nextjs.org/docs/messages/no-page-custom-font", "JSXOpeningElement", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["323", "324"], ["325", "326"], ["327", "328"], ["329", "330"], ["331", "332"], ["333", "334"], ["335", "336"], "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", ["337", "338"], "'email' is defined but never used.", ["339", "340"], "@typescript-eslint/no-namespace", "ES2015 module syntax is preferred over namespaces.", "TSModuleDeclaration", "moduleSyntaxIsPreferred", ["341", "342"], ["343", "344"], ["345", "346"], ["347", "348"], ["349", "350"], ["351", "352"], ["353", "354"], ["355", "356"], ["357", "358"], ["359", "360"], ["361", "362"], ["363", "364"], ["365", "366"], ["367", "368"], ["369", "370"], ["371", "372"], ["373", "374"], ["375", "376"], ["377", "378"], ["379", "380"], ["381", "382"], ["383", "384"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["385", "386"], ["387", "388"], ["389", "390"], ["391", "392"], ["393", "394"], ["395", "396"], ["397", "398"], ["399", "400"], ["401", "402"], ["403", "404"], ["405", "406"], ["407", "408"], ["409", "410"], ["411", "412"], ["413", "414"], ["415", "416"], ["417", "418"], ["419", "420"], ["421", "422"], ["423", "424"], ["425", "426"], ["427", "428"], ["429", "430"], ["431", "432"], ["433", "434"], {"messageId": "435", "fix": "436", "desc": "437"}, {"messageId": "438", "fix": "439", "desc": "440"}, {"messageId": "435", "fix": "441", "desc": "437"}, {"messageId": "438", "fix": "442", "desc": "440"}, {"messageId": "435", "fix": "443", "desc": "437"}, {"messageId": "438", "fix": "444", "desc": "440"}, {"messageId": "435", "fix": "445", "desc": "437"}, {"messageId": "438", "fix": "446", "desc": "440"}, {"messageId": "435", "fix": "447", "desc": "437"}, {"messageId": "438", "fix": "448", "desc": "440"}, {"messageId": "435", "fix": "449", "desc": "437"}, {"messageId": "438", "fix": "450", "desc": "440"}, {"messageId": "435", "fix": "451", "desc": "437"}, {"messageId": "438", "fix": "452", "desc": "440"}, {"messageId": "435", "fix": "453", "desc": "437"}, {"messageId": "438", "fix": "454", "desc": "440"}, {"messageId": "435", "fix": "455", "desc": "437"}, {"messageId": "438", "fix": "456", "desc": "440"}, {"messageId": "435", "fix": "457", "desc": "437"}, {"messageId": "438", "fix": "458", "desc": "440"}, {"messageId": "435", "fix": "459", "desc": "437"}, {"messageId": "438", "fix": "460", "desc": "440"}, {"messageId": "435", "fix": "461", "desc": "437"}, {"messageId": "438", "fix": "462", "desc": "440"}, {"messageId": "435", "fix": "463", "desc": "437"}, {"messageId": "438", "fix": "464", "desc": "440"}, {"messageId": "435", "fix": "465", "desc": "437"}, {"messageId": "438", "fix": "466", "desc": "440"}, {"messageId": "435", "fix": "467", "desc": "437"}, {"messageId": "438", "fix": "468", "desc": "440"}, {"messageId": "435", "fix": "469", "desc": "437"}, {"messageId": "438", "fix": "470", "desc": "440"}, {"messageId": "435", "fix": "471", "desc": "437"}, {"messageId": "438", "fix": "472", "desc": "440"}, {"messageId": "435", "fix": "473", "desc": "437"}, {"messageId": "438", "fix": "474", "desc": "440"}, {"messageId": "435", "fix": "475", "desc": "437"}, {"messageId": "438", "fix": "476", "desc": "440"}, {"messageId": "435", "fix": "477", "desc": "437"}, {"messageId": "438", "fix": "478", "desc": "440"}, {"messageId": "435", "fix": "479", "desc": "437"}, {"messageId": "438", "fix": "480", "desc": "440"}, {"messageId": "435", "fix": "481", "desc": "437"}, {"messageId": "438", "fix": "482", "desc": "440"}, {"messageId": "435", "fix": "483", "desc": "437"}, {"messageId": "438", "fix": "484", "desc": "440"}, {"messageId": "435", "fix": "485", "desc": "437"}, {"messageId": "438", "fix": "486", "desc": "440"}, {"messageId": "435", "fix": "487", "desc": "437"}, {"messageId": "438", "fix": "488", "desc": "440"}, {"messageId": "435", "fix": "489", "desc": "437"}, {"messageId": "438", "fix": "490", "desc": "440"}, {"messageId": "435", "fix": "491", "desc": "437"}, {"messageId": "438", "fix": "492", "desc": "440"}, {"messageId": "435", "fix": "493", "desc": "437"}, {"messageId": "438", "fix": "494", "desc": "440"}, {"messageId": "435", "fix": "495", "desc": "437"}, {"messageId": "438", "fix": "496", "desc": "440"}, {"messageId": "435", "fix": "497", "desc": "437"}, {"messageId": "438", "fix": "498", "desc": "440"}, {"messageId": "435", "fix": "499", "desc": "437"}, {"messageId": "438", "fix": "500", "desc": "440"}, {"messageId": "501", "data": "502", "fix": "503", "desc": "504"}, {"messageId": "501", "data": "505", "fix": "506", "desc": "507"}, {"messageId": "501", "data": "508", "fix": "509", "desc": "504"}, {"messageId": "501", "data": "510", "fix": "511", "desc": "507"}, {"messageId": "435", "fix": "512", "desc": "437"}, {"messageId": "438", "fix": "513", "desc": "440"}, {"messageId": "435", "fix": "514", "desc": "437"}, {"messageId": "438", "fix": "515", "desc": "440"}, {"messageId": "435", "fix": "516", "desc": "437"}, {"messageId": "438", "fix": "517", "desc": "440"}, {"messageId": "435", "fix": "518", "desc": "437"}, {"messageId": "438", "fix": "519", "desc": "440"}, {"messageId": "435", "fix": "520", "desc": "437"}, {"messageId": "438", "fix": "521", "desc": "440"}, {"messageId": "435", "fix": "522", "desc": "437"}, {"messageId": "438", "fix": "523", "desc": "440"}, {"messageId": "435", "fix": "524", "desc": "437"}, {"messageId": "438", "fix": "525", "desc": "440"}, {"messageId": "435", "fix": "526", "desc": "437"}, {"messageId": "438", "fix": "527", "desc": "440"}, {"messageId": "435", "fix": "528", "desc": "437"}, {"messageId": "438", "fix": "529", "desc": "440"}, {"messageId": "435", "fix": "530", "desc": "437"}, {"messageId": "438", "fix": "531", "desc": "440"}, {"messageId": "435", "fix": "532", "desc": "437"}, {"messageId": "438", "fix": "533", "desc": "440"}, {"messageId": "435", "fix": "534", "desc": "437"}, {"messageId": "438", "fix": "535", "desc": "440"}, {"messageId": "435", "fix": "536", "desc": "437"}, {"messageId": "438", "fix": "537", "desc": "440"}, {"messageId": "435", "fix": "538", "desc": "437"}, {"messageId": "438", "fix": "539", "desc": "440"}, {"messageId": "435", "fix": "540", "desc": "437"}, {"messageId": "438", "fix": "541", "desc": "440"}, {"messageId": "435", "fix": "542", "desc": "437"}, {"messageId": "438", "fix": "543", "desc": "440"}, {"messageId": "435", "fix": "544", "desc": "437"}, {"messageId": "438", "fix": "545", "desc": "440"}, {"messageId": "435", "fix": "546", "desc": "437"}, {"messageId": "438", "fix": "547", "desc": "440"}, {"messageId": "435", "fix": "548", "desc": "437"}, {"messageId": "438", "fix": "549", "desc": "440"}, {"messageId": "435", "fix": "550", "desc": "437"}, {"messageId": "438", "fix": "551", "desc": "440"}, {"messageId": "435", "fix": "552", "desc": "437"}, {"messageId": "438", "fix": "553", "desc": "440"}, {"messageId": "435", "fix": "554", "desc": "437"}, {"messageId": "438", "fix": "555", "desc": "440"}, {"messageId": "435", "fix": "556", "desc": "437"}, {"messageId": "438", "fix": "557", "desc": "440"}, "suggestUnknown", {"range": "558", "text": "559"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "560", "text": "561"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "562", "text": "559"}, {"range": "563", "text": "561"}, {"range": "564", "text": "559"}, {"range": "565", "text": "561"}, {"range": "566", "text": "559"}, {"range": "567", "text": "561"}, {"range": "568", "text": "559"}, {"range": "569", "text": "561"}, {"range": "570", "text": "559"}, {"range": "571", "text": "561"}, {"range": "572", "text": "559"}, {"range": "573", "text": "561"}, {"range": "574", "text": "559"}, {"range": "575", "text": "561"}, {"range": "576", "text": "559"}, {"range": "577", "text": "561"}, {"range": "578", "text": "559"}, {"range": "579", "text": "561"}, {"range": "580", "text": "559"}, {"range": "581", "text": "561"}, {"range": "582", "text": "559"}, {"range": "583", "text": "561"}, {"range": "584", "text": "559"}, {"range": "585", "text": "561"}, {"range": "586", "text": "559"}, {"range": "587", "text": "561"}, {"range": "588", "text": "559"}, {"range": "589", "text": "561"}, {"range": "590", "text": "559"}, {"range": "591", "text": "561"}, {"range": "592", "text": "559"}, {"range": "593", "text": "561"}, {"range": "594", "text": "559"}, {"range": "595", "text": "561"}, {"range": "596", "text": "559"}, {"range": "597", "text": "561"}, {"range": "598", "text": "559"}, {"range": "599", "text": "561"}, {"range": "600", "text": "559"}, {"range": "601", "text": "561"}, {"range": "602", "text": "559"}, {"range": "603", "text": "561"}, {"range": "604", "text": "559"}, {"range": "605", "text": "561"}, {"range": "606", "text": "559"}, {"range": "607", "text": "561"}, {"range": "608", "text": "559"}, {"range": "609", "text": "561"}, {"range": "610", "text": "559"}, {"range": "611", "text": "561"}, {"range": "612", "text": "559"}, {"range": "613", "text": "561"}, {"range": "614", "text": "559"}, {"range": "615", "text": "561"}, {"range": "616", "text": "559"}, {"range": "617", "text": "561"}, {"range": "618", "text": "559"}, {"range": "619", "text": "561"}, {"range": "620", "text": "559"}, {"range": "621", "text": "561"}, "replaceEmptyObjectType", {"replacement": "622"}, {"range": "623", "text": "622"}, "Replace `{}` with `object`.", {"replacement": "559"}, {"range": "624", "text": "559"}, "Replace `{}` with `unknown`.", {"replacement": "622"}, {"range": "625", "text": "622"}, {"replacement": "559"}, {"range": "626", "text": "559"}, {"range": "627", "text": "559"}, {"range": "628", "text": "561"}, {"range": "629", "text": "559"}, {"range": "630", "text": "561"}, {"range": "631", "text": "559"}, {"range": "632", "text": "561"}, {"range": "633", "text": "559"}, {"range": "634", "text": "561"}, {"range": "635", "text": "559"}, {"range": "636", "text": "561"}, {"range": "637", "text": "559"}, {"range": "638", "text": "561"}, {"range": "639", "text": "559"}, {"range": "640", "text": "561"}, {"range": "641", "text": "559"}, {"range": "642", "text": "561"}, {"range": "643", "text": "559"}, {"range": "644", "text": "561"}, {"range": "645", "text": "559"}, {"range": "646", "text": "561"}, {"range": "647", "text": "559"}, {"range": "648", "text": "561"}, {"range": "649", "text": "559"}, {"range": "650", "text": "561"}, {"range": "651", "text": "559"}, {"range": "652", "text": "561"}, {"range": "653", "text": "559"}, {"range": "654", "text": "561"}, {"range": "655", "text": "559"}, {"range": "656", "text": "561"}, {"range": "657", "text": "559"}, {"range": "658", "text": "561"}, {"range": "659", "text": "559"}, {"range": "660", "text": "561"}, {"range": "661", "text": "559"}, {"range": "662", "text": "561"}, {"range": "663", "text": "559"}, {"range": "664", "text": "561"}, {"range": "665", "text": "559"}, {"range": "666", "text": "561"}, {"range": "667", "text": "559"}, {"range": "668", "text": "561"}, {"range": "669", "text": "559"}, {"range": "670", "text": "561"}, {"range": "671", "text": "559"}, {"range": "672", "text": "561"}, [210, 213], "unknown", [210, 213], "never", [914, 917], [914, 917], [1331, 1334], [1331, 1334], [1721, 1724], [1721, 1724], [2073, 2076], [2073, 2076], [2413, 2416], [2413, 2416], [2774, 2777], [2774, 2777], [1068, 1071], [1068, 1071], [158, 161], [158, 161], [784, 787], [784, 787], [1315, 1318], [1315, 1318], [1572, 1575], [1572, 1575], [1580, 1583], [1580, 1583], [1608, 1611], [1608, 1611], [1616, 1619], [1616, 1619], [2048, 2051], [2048, 2051], [2254, 2257], [2254, 2257], [2281, 2284], [2281, 2284], [2323, 2326], [2323, 2326], [2348, 2351], [2348, 2351], [2362, 2365], [2362, 2365], [2403, 2406], [2403, 2406], [2428, 2431], [2428, 2431], [2442, 2445], [2442, 2445], [2486, 2489], [2486, 2489], [2513, 2516], [2513, 2516], [2556, 2559], [2556, 2559], [2581, 2584], [2581, 2584], [2595, 2598], [2595, 2598], [1190, 1193], [1190, 1193], [1489, 1492], [1489, 1492], "object", [4618, 4620], [4618, 4620], [4709, 4711], [4709, 4711], [4372, 4375], [4372, 4375], [4424, 4427], [4424, 4427], [4462, 4465], [4462, 4465], [110, 113], [110, 113], [1163, 1166], [1163, 1166], [2154, 2157], [2154, 2157], [2484, 2487], [2484, 2487], [2790, 2793], [2790, 2793], [3102, 3105], [3102, 3105], [3425, 3428], [3425, 3428], [4095, 4098], [4095, 4098], [4453, 4456], [4453, 4456], [4808, 4811], [4808, 4811], [5224, 5227], [5224, 5227], [5535, 5538], [5535, 5538], [5737, 5740], [5737, 5740], [5930, 5933], [5930, 5933], [6136, 6139], [6136, 6139], [858, 861], [858, 861], [868, 871], [868, 871], [1168, 1171], [1168, 1171], [1178, 1181], [1178, 1181], [284, 287], [284, 287]]