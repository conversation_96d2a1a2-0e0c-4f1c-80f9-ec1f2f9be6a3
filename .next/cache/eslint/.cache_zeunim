[{"/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/about/page.tsx": "1", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/api/analytics/route.ts": "2", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/api/email/route.ts": "3", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/layout.tsx": "4", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/page.tsx": "5", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/robots.ts": "6", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/sitemap.ts": "7", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/summit/page.tsx": "8", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Accordion.tsx": "9", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/AnalyticsProvider.tsx": "10", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/ContactSection.tsx": "11", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/EmailForm.tsx": "12", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/FeatureCard.tsx": "13", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Footer.tsx": "14", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/GoogleAnalytics.tsx": "15", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Header.tsx": "16", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/JoinWaitlistButton.tsx": "17", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/ProcessStep.tsx": "18", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/SEO.tsx": "19", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/StructuredData.tsx": "20", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/index.ts": "21", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/index.ts": "22", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useAccordion.ts": "23", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useAnalytics.ts": "24", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useEmailForm.ts": "25", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useIntersectionObserver.ts": "26", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useMobileMenu.ts": "27", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useScrollAnimation.ts": "28", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/lib/api.ts": "29", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/lib/emailjs.ts": "30", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/api.ts": "31", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/index.ts": "32", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/ui.ts": "33", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/analytics.ts": "34", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/constants.ts": "35", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/helpers.ts": "36", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/seo.ts": "37", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/contact/page.tsx": "38", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/about/AboutHero.tsx": "39", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/about/CorePrinciplesSection.tsx": "40", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/about/EntrepreneurshipSection.tsx": "41", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/about/LeadershipSection.tsx": "42", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/about/index.ts": "43", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/contact/ContactForm.tsx": "44", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/contact/ContactHero.tsx": "45", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/contact/ContactInfo.tsx": "46", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/contact/OfficeDetails.tsx": "47", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/contact/index.ts": "48", "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/home/<USER>": "56"}, {"size": 1187, "mtime": 1753119494534, "results": "57", "hashOfConfig": "58"}, {"size": 1649, "mtime": 1753116501405, "results": "59", "hashOfConfig": "58"}, {"size": 1438, "mtime": 1753116487395, "results": "60", "hashOfConfig": "58"}, {"size": 991, "mtime": 1753117278507, "results": "61", "hashOfConfig": "58"}, {"size": 848, "mtime": 1753119088763, "results": "62", "hashOfConfig": "58"}, {"size": 334, "mtime": 1753117017077, "results": "63", "hashOfConfig": "58"}, {"size": 632, "mtime": 1753117009362, "results": "64", "hashOfConfig": "58"}, {"size": 7144, "mtime": 1753116463475, "results": "65", "hashOfConfig": "58"}, {"size": 1349, "mtime": 1753116884623, "results": "66", "hashOfConfig": "58"}, {"size": 1226, "mtime": 1753118079092, "results": "67", "hashOfConfig": "58"}, {"size": 1465, "mtime": 1753115888162, "results": "68", "hashOfConfig": "58"}, {"size": 1852, "mtime": 1753116545075, "results": "69", "hashOfConfig": "58"}, {"size": 1920, "mtime": 1753115845389, "results": "70", "hashOfConfig": "58"}, {"size": 1813, "mtime": 1753117522879, "results": "71", "hashOfConfig": "58"}, {"size": 959, "mtime": 1753117252572, "results": "72", "hashOfConfig": "58"}, {"size": 4448, "mtime": 1753117509689, "results": "73", "hashOfConfig": "58"}, {"size": 856, "mtime": 1753116973451, "results": "74", "hashOfConfig": "58"}, {"size": 1001, "mtime": 1753115855335, "results": "75", "hashOfConfig": "58"}, {"size": 1784, "mtime": 1753115901331, "results": "76", "hashOfConfig": "58"}, {"size": 296, "mtime": 1753117535307, "results": "77", "hashOfConfig": "58"}, {"size": 888, "mtime": 1753119541708, "results": "78", "hashOfConfig": "58"}, {"size": 372, "mtime": 1753116987165, "results": "79", "hashOfConfig": "58"}, {"size": 2532, "mtime": 1753116826391, "results": "80", "hashOfConfig": "58"}, {"size": 3103, "mtime": 1753116844351, "results": "81", "hashOfConfig": "58"}, {"size": 1834, "mtime": 1753116530230, "results": "82", "hashOfConfig": "58"}, {"size": 1461, "mtime": 1753116858880, "results": "83", "hashOfConfig": "58"}, {"size": 1702, "mtime": 1753116810153, "results": "84", "hashOfConfig": "58"}, {"size": 2853, "mtime": 1753116014729, "results": "85", "hashOfConfig": "58"}, {"size": 1954, "mtime": 1753116516211, "results": "86", "hashOfConfig": "58"}, {"size": 709, "mtime": 1753115711389, "results": "87", "hashOfConfig": "58"}, {"size": 3091, "mtime": 1753116637393, "results": "88", "hashOfConfig": "58"}, {"size": 4707, "mtime": 1753118333891, "results": "89", "hashOfConfig": "58"}, {"size": 5765, "mtime": 1753116688623, "results": "90", "hashOfConfig": "58"}, {"size": 6216, "mtime": 1753118145789, "results": "91", "hashOfConfig": "58"}, {"size": 6508, "mtime": 1753115692846, "results": "92", "hashOfConfig": "58"}, {"size": 2128, "mtime": 1753115724845, "results": "93", "hashOfConfig": "58"}, {"size": 5044, "mtime": 1753117047662, "results": "94", "hashOfConfig": "58"}, {"size": 935, "mtime": 1753119195337, "results": "95", "hashOfConfig": "58"}, {"size": 1541, "mtime": 1753119209383, "results": "96", "hashOfConfig": "58"}, {"size": 3433, "mtime": 1753119255997, "results": "97", "hashOfConfig": "58"}, {"size": 4373, "mtime": 1753119643479, "results": "98", "hashOfConfig": "58"}, {"size": 4194, "mtime": 1753119658477, "results": "99", "hashOfConfig": "58"}, {"size": 232, "mtime": 1753119292866, "results": "100", "hashOfConfig": "58"}, {"size": 6958, "mtime": 1753119626330, "results": "101", "hashOfConfig": "58"}, {"size": 711, "mtime": 1753119105379, "results": "102", "hashOfConfig": "58"}, {"size": 2562, "mtime": 1753119597458, "results": "103", "hashOfConfig": "58"}, {"size": 3023, "mtime": 1753119173333, "results": "104", "hashOfConfig": "58"}, {"size": 184, "mtime": 1753119183162, "results": "105", "hashOfConfig": "58"}, {"size": 1354, "mtime": 1753119027478, "results": "106", "hashOfConfig": "58"}, {"size": 4488, "mtime": 1753118960474, "results": "107", "hashOfConfig": "58"}, {"size": 2229, "mtime": 1753118909368, "results": "108", "hashOfConfig": "58"}, {"size": 2489, "mtime": 1753118892463, "results": "109", "hashOfConfig": "58"}, {"size": 4550, "mtime": 1753118934907, "results": "110", "hashOfConfig": "58"}, {"size": 3584, "mtime": 1753118982614, "results": "111", "hashOfConfig": "58"}, {"size": 6111, "mtime": 1753119673185, "results": "112", "hashOfConfig": "58"}, {"size": 361, "mtime": 1753119038045, "results": "113", "hashOfConfig": "58"}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "txeug1", {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/about/page.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/api/analytics/route.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/api/email/route.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/layout.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/page.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/robots.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/sitemap.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/summit/page.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Accordion.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/AnalyticsProvider.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/ContactSection.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/EmailForm.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/FeatureCard.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Footer.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/GoogleAnalytics.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/Header.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/JoinWaitlistButton.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/ProcessStep.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/SEO.tsx", ["282"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/StructuredData.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/index.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/index.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useAccordion.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useAnalytics.ts", ["283", "284", "285", "286", "287", "288", "289"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useEmailForm.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useIntersectionObserver.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useMobileMenu.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/hooks/useScrollAnimation.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/lib/api.ts", ["290", "291", "292"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/lib/emailjs.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/api.ts", ["293", "294", "295", "296", "297", "298", "299", "300", "301", "302", "303", "304", "305", "306", "307", "308", "309", "310", "311", "312", "313", "314", "315"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/index.ts", ["316", "317", "318", "319"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/types/ui.ts", ["320", "321", "322"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/analytics.ts", ["323", "324", "325", "326", "327", "328", "329", "330", "331", "332", "333", "334", "335", "336", "337"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/constants.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/helpers.ts", ["338", "339", "340", "341"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/utils/seo.ts", ["342"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/app/contact/page.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/about/AboutHero.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/about/CorePrinciplesSection.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/about/EntrepreneurshipSection.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/about/LeadershipSection.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/about/index.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/contact/ContactForm.tsx", ["343"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/contact/ContactHero.tsx", ["344", "345"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/contact/ContactInfo.tsx", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/contact/OfficeDetails.tsx", ["346"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/contact/index.ts", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/home/<USER>", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/home/<USER>", ["347"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/home/<USER>", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/home/<USER>", [], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/home/<USER>", ["348", "349"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/home/<USER>", ["350"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/home/<USER>", ["351", "352", "353", "354"], [], "/Users/<USER>/Desktop/Fundfina/madad.github.io/src/components/home/<USER>", [], [], {"ruleId": "355", "severity": 1, "message": "356", "line": 41, "column": 7, "nodeType": "357", "endLine": 44, "endColumn": 9}, {"ruleId": "358", "severity": 1, "message": "359", "line": 9, "column": 21, "nodeType": "360", "messageId": "361", "endLine": 9, "endColumn": 24, "suggestions": "362"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 38, "column": 37, "nodeType": "360", "messageId": "361", "endLine": 38, "endColumn": 40, "suggestions": "363"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 55, "column": 37, "nodeType": "360", "messageId": "361", "endLine": 55, "endColumn": 40, "suggestions": "364"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 70, "column": 37, "nodeType": "360", "messageId": "361", "endLine": 70, "endColumn": 40, "suggestions": "365"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 84, "column": 37, "nodeType": "360", "messageId": "361", "endLine": 84, "endColumn": 40, "suggestions": "366"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 97, "column": 37, "nodeType": "360", "messageId": "361", "endLine": 97, "endColumn": 40, "suggestions": "367"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 111, "column": 37, "nodeType": "360", "messageId": "361", "endLine": 111, "endColumn": 40, "suggestions": "368"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 41, "column": 14, "nodeType": null, "messageId": "371", "endLine": 41, "endColumn": 19}, {"ruleId": "358", "severity": 1, "message": "359", "line": 49, "column": 68, "nodeType": "360", "messageId": "361", "endLine": 49, "endColumn": 71, "suggestions": "372"}, {"ruleId": "369", "severity": 1, "message": "373", "line": 66, "column": 28, "nodeType": null, "messageId": "371", "endLine": 66, "endColumn": 33}, {"ruleId": "358", "severity": 1, "message": "359", "line": 6, "column": 38, "nodeType": "360", "messageId": "361", "endLine": 6, "endColumn": 41, "suggestions": "374"}, {"ruleId": "375", "severity": 1, "message": "376", "line": 21, "column": 8, "nodeType": "377", "messageId": "378", "endLine": 35, "endColumn": 2}, {"ruleId": "375", "severity": 1, "message": "376", "line": 37, "column": 8, "nodeType": "377", "messageId": "378", "endLine": 55, "endColumn": 2}, {"ruleId": "358", "severity": 1, "message": "359", "line": 41, "column": 37, "nodeType": "360", "messageId": "361", "endLine": 41, "endColumn": 40, "suggestions": "379"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 70, "column": 29, "nodeType": "360", "messageId": "361", "endLine": 70, "endColumn": 32, "suggestions": "380"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 79, "column": 26, "nodeType": "360", "messageId": "361", "endLine": 79, "endColumn": 29, "suggestions": "381"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 79, "column": 34, "nodeType": "360", "messageId": "361", "endLine": 79, "endColumn": 37, "suggestions": "382"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 80, "column": 24, "nodeType": "360", "messageId": "361", "endLine": 80, "endColumn": 27, "suggestions": "383"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 80, "column": 32, "nodeType": "360", "messageId": "361", "endLine": 80, "endColumn": 35, "suggestions": "384"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 98, "column": 22, "nodeType": "360", "messageId": "361", "endLine": 98, "endColumn": 25, "suggestions": "385"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 108, "column": 11, "nodeType": "360", "messageId": "361", "endLine": 108, "endColumn": 14, "suggestions": "386"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 108, "column": 38, "nodeType": "360", "messageId": "361", "endLine": 108, "endColumn": 41, "suggestions": "387"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 109, "column": 12, "nodeType": "360", "messageId": "361", "endLine": 109, "endColumn": 15, "suggestions": "388"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 109, "column": 37, "nodeType": "360", "messageId": "361", "endLine": 109, "endColumn": 40, "suggestions": "389"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 109, "column": 51, "nodeType": "360", "messageId": "361", "endLine": 109, "endColumn": 54, "suggestions": "390"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 110, "column": 11, "nodeType": "360", "messageId": "361", "endLine": 110, "endColumn": 14, "suggestions": "391"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 110, "column": 36, "nodeType": "360", "messageId": "361", "endLine": 110, "endColumn": 39, "suggestions": "392"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 110, "column": 50, "nodeType": "360", "messageId": "361", "endLine": 110, "endColumn": 53, "suggestions": "393"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 111, "column": 14, "nodeType": "360", "messageId": "361", "endLine": 111, "endColumn": 17, "suggestions": "394"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 111, "column": 41, "nodeType": "360", "messageId": "361", "endLine": 111, "endColumn": 44, "suggestions": "395"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 112, "column": 13, "nodeType": "360", "messageId": "361", "endLine": 112, "endColumn": 16, "suggestions": "396"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 112, "column": 38, "nodeType": "360", "messageId": "361", "endLine": 112, "endColumn": 41, "suggestions": "397"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 112, "column": 52, "nodeType": "360", "messageId": "361", "endLine": 112, "endColumn": 55, "suggestions": "398"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 73, "column": 34, "nodeType": "360", "messageId": "361", "endLine": 73, "endColumn": 37, "suggestions": "399"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 91, "column": 35, "nodeType": "360", "messageId": "361", "endLine": 91, "endColumn": 38, "suggestions": "400"}, {"ruleId": "401", "severity": 1, "message": "402", "line": 234, "column": 39, "nodeType": "403", "messageId": "404", "endLine": 234, "endColumn": 41, "suggestions": "405"}, {"ruleId": "401", "severity": 1, "message": "402", "line": 235, "column": 40, "nodeType": "403", "messageId": "404", "endLine": 235, "endColumn": 42, "suggestions": "406"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 182, "column": 35, "nodeType": "360", "messageId": "361", "endLine": 182, "endColumn": 38, "suggestions": "407"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 183, "column": 22, "nodeType": "360", "messageId": "361", "endLine": 183, "endColumn": 25, "suggestions": "408"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 184, "column": 34, "nodeType": "360", "messageId": "361", "endLine": 184, "endColumn": 37, "suggestions": "409"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 5, "column": 21, "nodeType": "360", "messageId": "361", "endLine": 5, "endColumn": 24, "suggestions": "410"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 54, "column": 32, "nodeType": "360", "messageId": "361", "endLine": 54, "endColumn": 35, "suggestions": "411"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 91, "column": 36, "nodeType": "360", "messageId": "361", "endLine": 91, "endColumn": 39, "suggestions": "412"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 105, "column": 36, "nodeType": "360", "messageId": "361", "endLine": 105, "endColumn": 39, "suggestions": "413"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 119, "column": 36, "nodeType": "360", "messageId": "361", "endLine": 119, "endColumn": 39, "suggestions": "414"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 132, "column": 36, "nodeType": "360", "messageId": "361", "endLine": 132, "endColumn": 39, "suggestions": "415"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 145, "column": 36, "nodeType": "360", "messageId": "361", "endLine": 145, "endColumn": 39, "suggestions": "416"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 173, "column": 36, "nodeType": "360", "messageId": "361", "endLine": 173, "endColumn": 39, "suggestions": "417"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 189, "column": 36, "nodeType": "360", "messageId": "361", "endLine": 189, "endColumn": 39, "suggestions": "418"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 204, "column": 36, "nodeType": "360", "messageId": "361", "endLine": 204, "endColumn": 39, "suggestions": "419"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 221, "column": 30, "nodeType": "360", "messageId": "361", "endLine": 221, "endColumn": 33, "suggestions": "420"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 233, "column": 35, "nodeType": "360", "messageId": "361", "endLine": 233, "endColumn": 38, "suggestions": "421"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 240, "column": 35, "nodeType": "360", "messageId": "361", "endLine": 240, "endColumn": 38, "suggestions": "422"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 246, "column": 35, "nodeType": "360", "messageId": "361", "endLine": 246, "endColumn": 38, "suggestions": "423"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 253, "column": 35, "nodeType": "360", "messageId": "361", "endLine": 253, "endColumn": 38, "suggestions": "424"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 37, "column": 46, "nodeType": "360", "messageId": "361", "endLine": 37, "endColumn": 49, "suggestions": "425"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 37, "column": 56, "nodeType": "360", "messageId": "361", "endLine": 37, "endColumn": 59, "suggestions": "426"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 49, "column": 46, "nodeType": "360", "messageId": "361", "endLine": 49, "endColumn": 49, "suggestions": "427"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 49, "column": 56, "nodeType": "360", "messageId": "361", "endLine": 49, "endColumn": 59, "suggestions": "428"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 13, "column": 20, "nodeType": "360", "messageId": "361", "endLine": 13, "endColumn": 23, "suggestions": "429"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 52, "column": 14, "nodeType": null, "messageId": "371", "endLine": 52, "endColumn": 19}, {"ruleId": "430", "severity": 2, "message": "431", "line": 11, "column": 50, "nodeType": "432", "messageId": "433", "suggestions": "434"}, {"ruleId": "430", "severity": 2, "message": "431", "line": 15, "column": 41, "nodeType": "432", "messageId": "433", "suggestions": "435"}, {"ruleId": "369", "severity": 1, "message": "436", "line": 1, "column": 52, "nodeType": null, "messageId": "371", "endLine": 1, "endColumn": 61}, {"ruleId": "369", "severity": 1, "message": "437", "line": 1, "column": 8, "nodeType": null, "messageId": "371", "endLine": 1, "endColumn": 13}, {"ruleId": "369", "severity": 1, "message": "438", "line": 68, "column": 40, "nodeType": null, "messageId": "371", "endLine": 68, "endColumn": 45}, {"ruleId": "369", "severity": 1, "message": "438", "line": 101, "column": 36, "nodeType": null, "messageId": "371", "endLine": 101, "endColumn": 41}, {"ruleId": "430", "severity": 2, "message": "431", "line": 36, "column": 24, "nodeType": "432", "messageId": "433", "suggestions": "439"}, {"ruleId": "430", "severity": 2, "message": "440", "line": 74, "column": 19, "nodeType": "432", "messageId": "433", "suggestions": "441"}, {"ruleId": "430", "severity": 2, "message": "440", "line": 74, "column": 41, "nodeType": "432", "messageId": "433", "suggestions": "442"}, {"ruleId": "430", "severity": 2, "message": "440", "line": 106, "column": 17, "nodeType": "432", "messageId": "433", "suggestions": "443"}, {"ruleId": "430", "severity": 2, "message": "440", "line": 106, "column": 60, "nodeType": "432", "messageId": "433", "suggestions": "444"}, "@next/next/no-page-custom-font", "Custom fonts not added in `pages/_document.js` will only load for a single page. This is discouraged. See: https://nextjs.org/docs/messages/no-page-custom-font", "JSXOpeningElement", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["445", "446"], ["447", "448"], ["449", "450"], ["451", "452"], ["453", "454"], ["455", "456"], ["457", "458"], "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", ["459", "460"], "'email' is defined but never used.", ["461", "462"], "@typescript-eslint/no-namespace", "ES2015 module syntax is preferred over namespaces.", "TSModuleDeclaration", "moduleSyntaxIsPreferred", ["463", "464"], ["465", "466"], ["467", "468"], ["469", "470"], ["471", "472"], ["473", "474"], ["475", "476"], ["477", "478"], ["479", "480"], ["481", "482"], ["483", "484"], ["485", "486"], ["487", "488"], ["489", "490"], ["491", "492"], ["493", "494"], ["495", "496"], ["497", "498"], ["499", "500"], ["501", "502"], ["503", "504"], ["505", "506"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["507", "508"], ["509", "510"], ["511", "512"], ["513", "514"], ["515", "516"], ["517", "518"], ["519", "520"], ["521", "522"], ["523", "524"], ["525", "526"], ["527", "528"], ["529", "530"], ["531", "532"], ["533", "534"], ["535", "536"], ["537", "538"], ["539", "540"], ["541", "542"], ["543", "544"], ["545", "546"], ["547", "548"], ["549", "550"], ["551", "552"], ["553", "554"], ["555", "556"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["557", "558", "559", "560"], ["561", "562", "563", "564"], "'PhoneIcon' is defined but never used.", "'Image' is defined but never used.", "'index' is defined but never used.", ["565", "566", "567", "568"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["569", "570", "571", "572"], ["573", "574", "575", "576"], ["577", "578", "579", "580"], ["581", "582", "583", "584"], {"messageId": "585", "fix": "586", "desc": "587"}, {"messageId": "588", "fix": "589", "desc": "590"}, {"messageId": "585", "fix": "591", "desc": "587"}, {"messageId": "588", "fix": "592", "desc": "590"}, {"messageId": "585", "fix": "593", "desc": "587"}, {"messageId": "588", "fix": "594", "desc": "590"}, {"messageId": "585", "fix": "595", "desc": "587"}, {"messageId": "588", "fix": "596", "desc": "590"}, {"messageId": "585", "fix": "597", "desc": "587"}, {"messageId": "588", "fix": "598", "desc": "590"}, {"messageId": "585", "fix": "599", "desc": "587"}, {"messageId": "588", "fix": "600", "desc": "590"}, {"messageId": "585", "fix": "601", "desc": "587"}, {"messageId": "588", "fix": "602", "desc": "590"}, {"messageId": "585", "fix": "603", "desc": "587"}, {"messageId": "588", "fix": "604", "desc": "590"}, {"messageId": "585", "fix": "605", "desc": "587"}, {"messageId": "588", "fix": "606", "desc": "590"}, {"messageId": "585", "fix": "607", "desc": "587"}, {"messageId": "588", "fix": "608", "desc": "590"}, {"messageId": "585", "fix": "609", "desc": "587"}, {"messageId": "588", "fix": "610", "desc": "590"}, {"messageId": "585", "fix": "611", "desc": "587"}, {"messageId": "588", "fix": "612", "desc": "590"}, {"messageId": "585", "fix": "613", "desc": "587"}, {"messageId": "588", "fix": "614", "desc": "590"}, {"messageId": "585", "fix": "615", "desc": "587"}, {"messageId": "588", "fix": "616", "desc": "590"}, {"messageId": "585", "fix": "617", "desc": "587"}, {"messageId": "588", "fix": "618", "desc": "590"}, {"messageId": "585", "fix": "619", "desc": "587"}, {"messageId": "588", "fix": "620", "desc": "590"}, {"messageId": "585", "fix": "621", "desc": "587"}, {"messageId": "588", "fix": "622", "desc": "590"}, {"messageId": "585", "fix": "623", "desc": "587"}, {"messageId": "588", "fix": "624", "desc": "590"}, {"messageId": "585", "fix": "625", "desc": "587"}, {"messageId": "588", "fix": "626", "desc": "590"}, {"messageId": "585", "fix": "627", "desc": "587"}, {"messageId": "588", "fix": "628", "desc": "590"}, {"messageId": "585", "fix": "629", "desc": "587"}, {"messageId": "588", "fix": "630", "desc": "590"}, {"messageId": "585", "fix": "631", "desc": "587"}, {"messageId": "588", "fix": "632", "desc": "590"}, {"messageId": "585", "fix": "633", "desc": "587"}, {"messageId": "588", "fix": "634", "desc": "590"}, {"messageId": "585", "fix": "635", "desc": "587"}, {"messageId": "588", "fix": "636", "desc": "590"}, {"messageId": "585", "fix": "637", "desc": "587"}, {"messageId": "588", "fix": "638", "desc": "590"}, {"messageId": "585", "fix": "639", "desc": "587"}, {"messageId": "588", "fix": "640", "desc": "590"}, {"messageId": "585", "fix": "641", "desc": "587"}, {"messageId": "588", "fix": "642", "desc": "590"}, {"messageId": "585", "fix": "643", "desc": "587"}, {"messageId": "588", "fix": "644", "desc": "590"}, {"messageId": "585", "fix": "645", "desc": "587"}, {"messageId": "588", "fix": "646", "desc": "590"}, {"messageId": "585", "fix": "647", "desc": "587"}, {"messageId": "588", "fix": "648", "desc": "590"}, {"messageId": "585", "fix": "649", "desc": "587"}, {"messageId": "588", "fix": "650", "desc": "590"}, {"messageId": "651", "data": "652", "fix": "653", "desc": "654"}, {"messageId": "651", "data": "655", "fix": "656", "desc": "657"}, {"messageId": "651", "data": "658", "fix": "659", "desc": "654"}, {"messageId": "651", "data": "660", "fix": "661", "desc": "657"}, {"messageId": "585", "fix": "662", "desc": "587"}, {"messageId": "588", "fix": "663", "desc": "590"}, {"messageId": "585", "fix": "664", "desc": "587"}, {"messageId": "588", "fix": "665", "desc": "590"}, {"messageId": "585", "fix": "666", "desc": "587"}, {"messageId": "588", "fix": "667", "desc": "590"}, {"messageId": "585", "fix": "668", "desc": "587"}, {"messageId": "588", "fix": "669", "desc": "590"}, {"messageId": "585", "fix": "670", "desc": "587"}, {"messageId": "588", "fix": "671", "desc": "590"}, {"messageId": "585", "fix": "672", "desc": "587"}, {"messageId": "588", "fix": "673", "desc": "590"}, {"messageId": "585", "fix": "674", "desc": "587"}, {"messageId": "588", "fix": "675", "desc": "590"}, {"messageId": "585", "fix": "676", "desc": "587"}, {"messageId": "588", "fix": "677", "desc": "590"}, {"messageId": "585", "fix": "678", "desc": "587"}, {"messageId": "588", "fix": "679", "desc": "590"}, {"messageId": "585", "fix": "680", "desc": "587"}, {"messageId": "588", "fix": "681", "desc": "590"}, {"messageId": "585", "fix": "682", "desc": "587"}, {"messageId": "588", "fix": "683", "desc": "590"}, {"messageId": "585", "fix": "684", "desc": "587"}, {"messageId": "588", "fix": "685", "desc": "590"}, {"messageId": "585", "fix": "686", "desc": "587"}, {"messageId": "588", "fix": "687", "desc": "590"}, {"messageId": "585", "fix": "688", "desc": "587"}, {"messageId": "588", "fix": "689", "desc": "590"}, {"messageId": "585", "fix": "690", "desc": "587"}, {"messageId": "588", "fix": "691", "desc": "590"}, {"messageId": "585", "fix": "692", "desc": "587"}, {"messageId": "588", "fix": "693", "desc": "590"}, {"messageId": "585", "fix": "694", "desc": "587"}, {"messageId": "588", "fix": "695", "desc": "590"}, {"messageId": "585", "fix": "696", "desc": "587"}, {"messageId": "588", "fix": "697", "desc": "590"}, {"messageId": "585", "fix": "698", "desc": "587"}, {"messageId": "588", "fix": "699", "desc": "590"}, {"messageId": "585", "fix": "700", "desc": "587"}, {"messageId": "588", "fix": "701", "desc": "590"}, {"messageId": "585", "fix": "702", "desc": "587"}, {"messageId": "588", "fix": "703", "desc": "590"}, {"messageId": "585", "fix": "704", "desc": "587"}, {"messageId": "588", "fix": "705", "desc": "590"}, {"messageId": "585", "fix": "706", "desc": "587"}, {"messageId": "588", "fix": "707", "desc": "590"}, {"messageId": "708", "data": "709", "fix": "710", "desc": "711"}, {"messageId": "708", "data": "712", "fix": "713", "desc": "714"}, {"messageId": "708", "data": "715", "fix": "716", "desc": "717"}, {"messageId": "708", "data": "718", "fix": "719", "desc": "720"}, {"messageId": "708", "data": "721", "fix": "722", "desc": "711"}, {"messageId": "708", "data": "723", "fix": "724", "desc": "714"}, {"messageId": "708", "data": "725", "fix": "726", "desc": "717"}, {"messageId": "708", "data": "727", "fix": "728", "desc": "720"}, {"messageId": "708", "data": "729", "fix": "730", "desc": "711"}, {"messageId": "708", "data": "731", "fix": "732", "desc": "714"}, {"messageId": "708", "data": "733", "fix": "734", "desc": "717"}, {"messageId": "708", "data": "735", "fix": "736", "desc": "720"}, {"messageId": "708", "data": "737", "fix": "738", "desc": "739"}, {"messageId": "708", "data": "740", "fix": "741", "desc": "742"}, {"messageId": "708", "data": "743", "fix": "744", "desc": "745"}, {"messageId": "708", "data": "746", "fix": "747", "desc": "748"}, {"messageId": "708", "data": "749", "fix": "750", "desc": "739"}, {"messageId": "708", "data": "751", "fix": "752", "desc": "742"}, {"messageId": "708", "data": "753", "fix": "754", "desc": "745"}, {"messageId": "708", "data": "755", "fix": "756", "desc": "748"}, {"messageId": "708", "data": "757", "fix": "758", "desc": "739"}, {"messageId": "708", "data": "759", "fix": "760", "desc": "742"}, {"messageId": "708", "data": "761", "fix": "762", "desc": "745"}, {"messageId": "708", "data": "763", "fix": "764", "desc": "748"}, {"messageId": "708", "data": "765", "fix": "766", "desc": "739"}, {"messageId": "708", "data": "767", "fix": "768", "desc": "742"}, {"messageId": "708", "data": "769", "fix": "770", "desc": "745"}, {"messageId": "708", "data": "771", "fix": "772", "desc": "748"}, "suggestUnknown", {"range": "773", "text": "774"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "775", "text": "776"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "777", "text": "774"}, {"range": "778", "text": "776"}, {"range": "779", "text": "774"}, {"range": "780", "text": "776"}, {"range": "781", "text": "774"}, {"range": "782", "text": "776"}, {"range": "783", "text": "774"}, {"range": "784", "text": "776"}, {"range": "785", "text": "774"}, {"range": "786", "text": "776"}, {"range": "787", "text": "774"}, {"range": "788", "text": "776"}, {"range": "789", "text": "774"}, {"range": "790", "text": "776"}, {"range": "791", "text": "774"}, {"range": "792", "text": "776"}, {"range": "793", "text": "774"}, {"range": "794", "text": "776"}, {"range": "795", "text": "774"}, {"range": "796", "text": "776"}, {"range": "797", "text": "774"}, {"range": "798", "text": "776"}, {"range": "799", "text": "774"}, {"range": "800", "text": "776"}, {"range": "801", "text": "774"}, {"range": "802", "text": "776"}, {"range": "803", "text": "774"}, {"range": "804", "text": "776"}, {"range": "805", "text": "774"}, {"range": "806", "text": "776"}, {"range": "807", "text": "774"}, {"range": "808", "text": "776"}, {"range": "809", "text": "774"}, {"range": "810", "text": "776"}, {"range": "811", "text": "774"}, {"range": "812", "text": "776"}, {"range": "813", "text": "774"}, {"range": "814", "text": "776"}, {"range": "815", "text": "774"}, {"range": "816", "text": "776"}, {"range": "817", "text": "774"}, {"range": "818", "text": "776"}, {"range": "819", "text": "774"}, {"range": "820", "text": "776"}, {"range": "821", "text": "774"}, {"range": "822", "text": "776"}, {"range": "823", "text": "774"}, {"range": "824", "text": "776"}, {"range": "825", "text": "774"}, {"range": "826", "text": "776"}, {"range": "827", "text": "774"}, {"range": "828", "text": "776"}, {"range": "829", "text": "774"}, {"range": "830", "text": "776"}, {"range": "831", "text": "774"}, {"range": "832", "text": "776"}, {"range": "833", "text": "774"}, {"range": "834", "text": "776"}, {"range": "835", "text": "774"}, {"range": "836", "text": "776"}, "replaceEmptyObjectType", {"replacement": "837"}, {"range": "838", "text": "837"}, "Replace `{}` with `object`.", {"replacement": "774"}, {"range": "839", "text": "774"}, "Replace `{}` with `unknown`.", {"replacement": "837"}, {"range": "840", "text": "837"}, {"replacement": "774"}, {"range": "841", "text": "774"}, {"range": "842", "text": "774"}, {"range": "843", "text": "776"}, {"range": "844", "text": "774"}, {"range": "845", "text": "776"}, {"range": "846", "text": "774"}, {"range": "847", "text": "776"}, {"range": "848", "text": "774"}, {"range": "849", "text": "776"}, {"range": "850", "text": "774"}, {"range": "851", "text": "776"}, {"range": "852", "text": "774"}, {"range": "853", "text": "776"}, {"range": "854", "text": "774"}, {"range": "855", "text": "776"}, {"range": "856", "text": "774"}, {"range": "857", "text": "776"}, {"range": "858", "text": "774"}, {"range": "859", "text": "776"}, {"range": "860", "text": "774"}, {"range": "861", "text": "776"}, {"range": "862", "text": "774"}, {"range": "863", "text": "776"}, {"range": "864", "text": "774"}, {"range": "865", "text": "776"}, {"range": "866", "text": "774"}, {"range": "867", "text": "776"}, {"range": "868", "text": "774"}, {"range": "869", "text": "776"}, {"range": "870", "text": "774"}, {"range": "871", "text": "776"}, {"range": "872", "text": "774"}, {"range": "873", "text": "776"}, {"range": "874", "text": "774"}, {"range": "875", "text": "776"}, {"range": "876", "text": "774"}, {"range": "877", "text": "776"}, {"range": "878", "text": "774"}, {"range": "879", "text": "776"}, {"range": "880", "text": "774"}, {"range": "881", "text": "776"}, {"range": "882", "text": "774"}, {"range": "883", "text": "776"}, {"range": "884", "text": "774"}, {"range": "885", "text": "776"}, {"range": "886", "text": "774"}, {"range": "887", "text": "776"}, "replaceWithAlt", {"alt": "888"}, {"range": "889", "text": "890"}, "Replace with `&apos;`.", {"alt": "891"}, {"range": "892", "text": "893"}, "Replace with `&lsquo;`.", {"alt": "894"}, {"range": "895", "text": "896"}, "Replace with `&#39;`.", {"alt": "897"}, {"range": "898", "text": "899"}, "Replace with `&rsquo;`.", {"alt": "888"}, {"range": "900", "text": "901"}, {"alt": "891"}, {"range": "902", "text": "903"}, {"alt": "894"}, {"range": "904", "text": "905"}, {"alt": "897"}, {"range": "906", "text": "907"}, {"alt": "888"}, {"range": "908", "text": "909"}, {"alt": "891"}, {"range": "910", "text": "911"}, {"alt": "894"}, {"range": "912", "text": "913"}, {"alt": "897"}, {"range": "914", "text": "915"}, {"alt": "916"}, {"range": "917", "text": "918"}, "Replace with `&quot;`.", {"alt": "919"}, {"range": "920", "text": "921"}, "Replace with `&ldquo;`.", {"alt": "922"}, {"range": "923", "text": "924"}, "Replace with `&#34;`.", {"alt": "925"}, {"range": "926", "text": "927"}, "Replace with `&rdquo;`.", {"alt": "916"}, {"range": "928", "text": "929"}, {"alt": "919"}, {"range": "930", "text": "931"}, {"alt": "922"}, {"range": "932", "text": "933"}, {"alt": "925"}, {"range": "934", "text": "935"}, {"alt": "916"}, {"range": "936", "text": "937"}, {"alt": "919"}, {"range": "938", "text": "939"}, {"alt": "922"}, {"range": "940", "text": "941"}, {"alt": "925"}, {"range": "942", "text": "943"}, {"alt": "916"}, {"range": "944", "text": "945"}, {"alt": "919"}, {"range": "946", "text": "947"}, {"alt": "922"}, {"range": "948", "text": "949"}, {"alt": "925"}, {"range": "950", "text": "951"}, [210, 213], "unknown", [210, 213], "never", [914, 917], [914, 917], [1331, 1334], [1331, 1334], [1721, 1724], [1721, 1724], [2073, 2076], [2073, 2076], [2413, 2416], [2413, 2416], [2774, 2777], [2774, 2777], [1068, 1071], [1068, 1071], [158, 161], [158, 161], [784, 787], [784, 787], [1315, 1318], [1315, 1318], [1572, 1575], [1572, 1575], [1580, 1583], [1580, 1583], [1608, 1611], [1608, 1611], [1616, 1619], [1616, 1619], [2048, 2051], [2048, 2051], [2254, 2257], [2254, 2257], [2281, 2284], [2281, 2284], [2323, 2326], [2323, 2326], [2348, 2351], [2348, 2351], [2362, 2365], [2362, 2365], [2403, 2406], [2403, 2406], [2428, 2431], [2428, 2431], [2442, 2445], [2442, 2445], [2486, 2489], [2486, 2489], [2513, 2516], [2513, 2516], [2556, 2559], [2556, 2559], [2581, 2584], [2581, 2584], [2595, 2598], [2595, 2598], [1190, 1193], [1190, 1193], [1489, 1492], [1489, 1492], "object", [4306, 4308], [4306, 4308], [4397, 4399], [4397, 4399], [4372, 4375], [4372, 4375], [4424, 4427], [4424, 4427], [4462, 4465], [4462, 4465], [110, 113], [110, 113], [1163, 1166], [1163, 1166], [2154, 2157], [2154, 2157], [2484, 2487], [2484, 2487], [2790, 2793], [2790, 2793], [3102, 3105], [3102, 3105], [3425, 3428], [3425, 3428], [4095, 4098], [4095, 4098], [4453, 4456], [4453, 4456], [4808, 4811], [4808, 4811], [5224, 5227], [5224, 5227], [5535, 5538], [5535, 5538], [5737, 5740], [5737, 5740], [5930, 5933], [5930, 5933], [6136, 6139], [6136, 6139], [858, 861], [858, 861], [868, 871], [868, 871], [1168, 1171], [1168, 1171], [1178, 1181], [1178, 1181], [284, 287], [284, 287], "&apos;", [409, 487], "\n            Have questions or need assistance? We&apos;re here to help.\n          ", "&lsquo;", [409, 487], "\n            Have questions or need assistance? We&lsquo;re here to help.\n          ", "&#39;", [409, 487], "\n            Have questions or need assistance? We&#39;re here to help.\n          ", "&rsquo;", [409, 487], "\n            Have questions or need assistance? We&rsquo;re here to help.\n          ", [563, 655], "\n            Reach out to our team and we&apos;ll get back to you as soon as possible.\n          ", [563, 655], "\n            Reach out to our team and we&lsquo;ll get back to you as soon as possible.\n          ", [563, 655], "\n            Reach out to our team and we&#39;ll get back to you as soon as possible.\n          ", [563, 655], "\n            Reach out to our team and we&rsquo;ll get back to you as soon as possible.\n          ", [1237, 1389], "\n            Whether you&apos;re a trading company, manufacturer, or service provider, \n            we understand your unique cash flow challenges\n          ", [1237, 1389], "\n            Whether you&lsquo;re a trading company, manufacturer, or service provider, \n            we understand your unique cash flow challenges\n          ", [1237, 1389], "\n            Whether you&#39;re a trading company, manufacturer, or service provider, \n            we understand your unique cash flow challenges\n          ", [1237, 1389], "\n            Whether you&rsquo;re a trading company, manufacturer, or service provider, \n            we understand your unique cash flow challenges\n          ", "&quot;", [2604, 2624], "\n                  &quot;", "&ldquo;", [2604, 2624], "\n                  &ldquo;", "&#34;", [2604, 2624], "\n                  &#34;", "&rdquo;", [2604, 2624], "\n                  &rdquo;", [2645, 2663], "&quot;\n                ", [2645, 2663], "&ldquo;\n                ", [2645, 2663], "&#34;\n                ", [2645, 2663], "&rdquo;\n                ", [3898, 3916], "\n                &quot;", [3898, 3916], "\n                &ldquo;", [3898, 3916], "\n                &#34;", [3898, 3916], "\n                &rdquo;", [3958, 3974], "&quot;\n              ", [3958, 3974], "&ldquo;\n              ", [3958, 3974], "&#34;\n              ", [3958, 3974], "&rdquo;\n              "]