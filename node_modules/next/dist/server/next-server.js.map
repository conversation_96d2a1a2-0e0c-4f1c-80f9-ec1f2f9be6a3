{"version": 3, "sources": ["../../src/server/next-server.ts"], "sourcesContent": ["import './node-environment'\nimport './require-hook'\nimport './node-polyfill-crypto'\n\nimport type { CacheFs } from '../shared/lib/utils'\nimport {\n  DecodeError,\n  PageNotFoundError,\n  MiddlewareNotFoundError,\n} from '../shared/lib/utils'\nimport type { MiddlewareManifest } from '../build/webpack/plugins/middleware-plugin'\nimport type RenderResult from './render-result'\nimport type { FetchEventResult } from './web/types'\nimport type { PrerenderManifest, RoutesManifest } from '../build'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport type { NextParsedUrlQuery, NextUrlWithParsedQuery } from './request-meta'\nimport type { Params } from './request/params'\nimport type { MiddlewareRouteMatch } from '../shared/lib/router/utils/middleware-route-matcher'\nimport type { RouteMatch } from './route-matches/route-match'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { ParsedUrl } from '../shared/lib/router/utils/parse-url'\nimport type { CacheControl } from './lib/cache-control'\nimport type { WaitUntil } from './after/builtin-request-context'\n\nimport fs from 'fs'\nimport { join, relative } from 'path'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport { addRequestMeta, getRequestMeta } from './request-meta'\nimport {\n  PAGES_MANIFEST,\n  BUILD_ID_FILE,\n  MIDDLEWARE_MANIFEST,\n  PRERENDER_MANIFEST,\n  ROUTES_MANIFEST,\n  CLIENT_PUBLIC_FILES_PATH,\n  APP_PATHS_MANIFEST,\n  SERVER_DIRECTORY,\n  NEXT_FONT_MANIFEST,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  FUNCTIONS_CONFIG_MANIFEST,\n} from '../shared/lib/constants'\nimport { findDir } from '../lib/find-pages-dir'\nimport { NodeNextRequest, NodeNextResponse } from './base-http/node'\nimport { sendRenderResult } from './send-payload'\nimport { parseUrl } from '../shared/lib/router/utils/parse-url'\nimport * as Log from '../build/output/log'\n\nimport type {\n  Options,\n  FindComponentsResult,\n  MiddlewareRoutingItem,\n  RequestContext,\n  NormalizedRouteManifest,\n  LoadedRenderOpts,\n  RouteHandler,\n  NextEnabledDirectories,\n  BaseRequestHandler,\n} from './base-server'\nimport BaseServer from './base-server'\nimport { getMaybePagePath, getPagePath } from './require'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { loadComponents } from './load-components'\nimport type { LoadComponentsReturnType } from './load-components'\nimport isError, { getProperError } from '../lib/is-error'\nimport { splitCookiesString, toNodeOutgoingHttpHeaders } from './web/utils'\nimport { getMiddlewareRouteMatcher } from '../shared/lib/router/utils/middleware-route-matcher'\nimport { loadEnvConfig } from '@next/env'\nimport { urlQueryToSearchParams } from '../shared/lib/router/utils/querystring'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { getNextPathnameInfo } from '../shared/lib/router/utils/get-next-pathname-info'\nimport { getCloneableBody } from './body-streams'\nimport { checkIsOnDemandRevalidate } from './api-utils'\nimport ResponseCache, {\n  CachedRouteKind,\n  type IncrementalResponseCacheEntry,\n} from './response-cache'\nimport { IncrementalCache } from './lib/incremental-cache'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\n\nimport { setHttpClientAndAgentOptions } from './setup-http-agent-env'\n\nimport { isPagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type { PagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type { MatchOptions } from './route-matcher-managers/route-matcher-manager'\nimport { BubbledError, getTracer } from './lib/trace/tracer'\nimport { NextNodeServerSpan } from './lib/trace/constants'\nimport { nodeFs } from './lib/node-fs-methods'\nimport { getRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { pipeToNodeResponse } from './pipe-readable'\nimport { createRequestResponseMocks } from './lib/mock-request'\nimport { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\nimport { signalFromNodeResponse } from './web/spec-extension/adapters/next-request'\nimport { loadManifest } from './load-manifest.external'\nimport { lazyRenderAppPage } from './route-modules/app-page/module.render'\nimport { lazyRenderPagesPage } from './route-modules/pages/module.render'\nimport { interopDefault } from '../lib/interop-default'\nimport { formatDynamicImportPath } from '../lib/format-dynamic-import-path'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport { isInterceptionRouteRewrite } from '../lib/generate-interception-routes-rewrites'\nimport type { ServerOnInstrumentationRequestError } from './app-render/types'\nimport { RouteKind } from './route-kind'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { AwaiterOnce } from './after/awaiter'\nimport { AsyncCallbackSet } from './lib/async-callback-set'\nimport { initializeCacheHandlers, setCacheHandler } from './use-cache/handlers'\nimport type { UnwrapPromise } from '../lib/coalesced-function'\nimport { populateStaticEnv } from '../lib/static-env'\nimport { isPostpone } from './lib/router-utils/is-postpone'\nimport { NodeModuleLoader } from './lib/module-loader/node-module-loader'\nimport { NoFallbackError } from '../shared/lib/no-fallback-error.external'\nimport {\n  ensureInstrumentationRegistered,\n  getInstrumentationModule,\n} from './lib/router-utils/instrumentation-globals.external'\nimport {\n  RouterServerContextSymbol,\n  routerServerGlobal,\n} from './lib/router-utils/router-server-context'\n\nexport * from './base-server'\n\ndeclare const __non_webpack_require__: NodeRequire\n\n// For module that can be both CJS or ESM\nconst dynamicImportEsmDefault = process.env.NEXT_MINIMAL\n  ? (id: string) =>\n      import(/* webpackIgnore: true */ id).then((mod) => mod.default || mod)\n  : (id: string) => import(id).then((mod) => mod.default || mod)\n\nexport type NodeRequestHandler = BaseRequestHandler<\n  IncomingMessage | NodeNextRequest,\n  ServerResponse | NodeNextResponse\n>\n\ntype NodeRouteHandler = RouteHandler<NodeNextRequest, NodeNextResponse>\n\nconst MiddlewareMatcherCache = new WeakMap<\n  MiddlewareManifest['middleware'][string],\n  MiddlewareRouteMatch\n>()\n\nfunction getMiddlewareMatcher(\n  info: MiddlewareManifest['middleware'][string]\n): MiddlewareRouteMatch {\n  const stored = MiddlewareMatcherCache.get(info)\n  if (stored) {\n    return stored\n  }\n\n  if (!Array.isArray(info.matchers)) {\n    throw new Error(\n      `Invariant: invalid matchers for middleware ${JSON.stringify(info)}`\n    )\n  }\n\n  const matcher = getMiddlewareRouteMatcher(info.matchers)\n  MiddlewareMatcherCache.set(info, matcher)\n  return matcher\n}\n\nfunction installProcessErrorHandlers(\n  shouldRemoveUncaughtErrorAndRejectionListeners: boolean\n) {\n  // The conventional wisdom of Node.js and other runtimes is to treat\n  // unhandled errors as fatal and exit the process.\n  //\n  // But Next.js is not a generic JS runtime — it's a specialized runtime for\n  // React Server Components.\n  //\n  // Many unhandled rejections are due to the late-awaiting pattern for\n  // prefetching data. In Next.js it's OK to call an async function without\n  // immediately awaiting it, to start the request as soon as possible\n  // without blocking unncessarily on the result. These can end up\n  // triggering an \"unhandledRejection\" if it later turns out that the\n  // data is not needed to render the page. Example:\n  //\n  //     const promise = fetchData()\n  //     const shouldShow = await checkCondition()\n  //     if (shouldShow) {\n  //       return <Component promise={promise} />\n  //     }\n  //\n  // In this example, `fetchData` is called immediately to start the request\n  // as soon as possible, but if `shouldShow` is false, then it will be\n  // discarded without unwrapping its result. If it errors, it will trigger\n  // an \"unhandledRejection\" event.\n  //\n  // Ideally, we would suppress these rejections completely without warning,\n  // because we don't consider them real errors. (TODO: Currently we do warn.)\n  //\n  // But regardless of whether we do or don't warn, we definitely shouldn't\n  // crash the entire process.\n  //\n  // Even a \"legit\" unhandled error unrelated to prefetching shouldn't\n  // prevent the rest of the page from rendering.\n  //\n  // So, we're going to intentionally override the default error handling\n  // behavior of the outer JS runtime to be more forgiving\n\n  // Remove any existing \"unhandledRejection\" and \"uncaughtException\" handlers.\n  // This is gated behind an experimental flag until we've considered the impact\n  // in various deployment environments. It's possible this may always need to\n  // be configurable.\n  if (shouldRemoveUncaughtErrorAndRejectionListeners) {\n    process.removeAllListeners('uncaughtException')\n    process.removeAllListeners('unhandledRejection')\n  }\n\n  // Install a new handler to prevent the process from crashing.\n  process.on('unhandledRejection', (reason: unknown) => {\n    if (isPostpone(reason)) {\n      // React postpones that are unhandled might end up logged here but they're\n      // not really errors. They're just part of rendering.\n      return\n    }\n    // Immediately log the error.\n    // TODO: Ideally, if we knew that this error was triggered by application\n    // code, we would suppress it entirely without logging. We can't reliably\n    // detect all of these, but when dynamicIO is enabled, we could suppress\n    // at least some of them by waiting to log the error until after all in-\n    // progress renders have completed. Then, only log errors for which there\n    // was not a corresponding \"rejectionHandled\" event.\n    console.error(reason)\n  })\n\n  process.on('rejectionHandled', () => {\n    // TODO: See note in the unhandledRejection handler above. In the future,\n    // we may use the \"rejectionHandled\" event to de-queue an error from\n    // being logged.\n  })\n\n  // Unhandled exceptions are errors triggered by non-async functions, so this\n  // is unrelated to the late-awaiting pattern. However, for similar reasons,\n  // we still shouldn't crash the process. Just log it.\n  process.on('uncaughtException', (reason: unknown) => {\n    if (isPostpone(reason)) {\n      return\n    }\n    console.error(reason)\n  })\n}\n\nexport default class NextNodeServer extends BaseServer<\n  Options,\n  NodeNextRequest,\n  NodeNextResponse\n> {\n  protected middlewareManifestPath: string\n  private _serverDistDir: string | undefined\n  private imageResponseCache?: ResponseCache\n  private registeredInstrumentation: boolean = false\n  protected renderWorkersPromises?: Promise<void>\n  protected dynamicRoutes?: {\n    match: import('../shared/lib/router/utils/route-matcher').RouteMatchFn\n    page: string\n    re: RegExp\n  }[]\n  private routerServerHandler?: (\n    req: IncomingMessage,\n    res: ServerResponse\n  ) => void\n\n  protected cleanupListeners = new AsyncCallbackSet()\n  protected internalWaitUntil: WaitUntil | undefined\n  private isDev: boolean\n  private sriEnabled: boolean\n\n  constructor(options: Options) {\n    // Initialize super class\n    super(options)\n\n    const isDev = options.dev ?? false\n    this.isDev = isDev\n    this.sriEnabled = Boolean(options.conf.experimental?.sri?.algorithm)\n\n    /**\n     * This sets environment variable to be used at the time of SSR by head.tsx.\n     * Using this from process.env allows targeting SSR by calling\n     * `process.env.__NEXT_OPTIMIZE_CSS`.\n     */\n    if (this.renderOpts.optimizeCss) {\n      process.env.__NEXT_OPTIMIZE_CSS = JSON.stringify(true)\n    }\n    if (this.renderOpts.nextScriptWorkers) {\n      process.env.__NEXT_SCRIPT_WORKERS = JSON.stringify(true)\n    }\n    process.env.NEXT_DEPLOYMENT_ID = this.nextConfig.experimental.useSkewCookie\n      ? ''\n      : this.nextConfig.deploymentId || ''\n\n    if (!this.minimalMode) {\n      this.imageResponseCache = new ResponseCache(this.minimalMode)\n    }\n\n    const { appDocumentPreloading } = this.nextConfig.experimental\n    const isDefaultEnabled = typeof appDocumentPreloading === 'undefined'\n\n    if (\n      !options.dev &&\n      (appDocumentPreloading === true ||\n        !(this.minimalMode && isDefaultEnabled))\n    ) {\n      // pre-warm _document and _app as these will be\n      // needed for most requests\n      loadComponents({\n        distDir: this.distDir,\n        page: '/_document',\n        isAppPath: false,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      }).catch(() => {})\n      loadComponents({\n        distDir: this.distDir,\n        page: '/_app',\n        isAppPath: false,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      }).catch(() => {})\n    }\n\n    if (\n      !options.dev &&\n      !this.minimalMode &&\n      this.nextConfig.experimental.preloadEntriesOnStart\n    ) {\n      this.unstable_preloadEntries()\n    }\n\n    if (!options.dev) {\n      const { dynamicRoutes = [] } = this.getRoutesManifest() ?? {}\n      this.dynamicRoutes = dynamicRoutes.map((r) => {\n        // TODO: can we just re-use the regex from the manifest?\n        const regex = getRouteRegex(r.page)\n        const match = getRouteMatcher(regex)\n\n        return {\n          match,\n          page: r.page,\n          re: regex.re,\n        }\n      })\n    }\n\n    // ensure options are set when loadConfig isn't called\n    setHttpClientAndAgentOptions(this.nextConfig)\n\n    // Intercept fetch and other testmode apis.\n    if (this.serverOptions.experimentalTestProxy) {\n      process.env.NEXT_PRIVATE_TEST_PROXY = 'true'\n      const { interceptTestApis } =\n        // eslint-disable-next-line @next/internal/typechecked-require -- experimental/testmode is not built ins next/dist/esm\n        require('next/dist/experimental/testmode/server') as typeof import('../experimental/testmode/server')\n      interceptTestApis()\n    }\n\n    this.middlewareManifestPath = join(this.serverDistDir, MIDDLEWARE_MANIFEST)\n\n    // This is just optimization to fire prepare as soon as possible. It will be\n    // properly awaited later. We add the catch here to ensure that it does not\n    // cause a unhandled promise rejection. The promise rejection will be\n    // handled later on via the `await` when the request handler is called.\n    if (!options.dev) {\n      this.prepare().catch((err) => {\n        console.error('Failed to prepare server', err)\n      })\n    }\n\n    // when using compile mode static env isn't inlined so we\n    // need to populate in normal runtime env\n    if (this.renderOpts.isExperimentalCompile) {\n      populateStaticEnv(this.nextConfig)\n    }\n\n    const shouldRemoveUncaughtErrorAndRejectionListeners = Boolean(\n      options.conf.experimental?.removeUncaughtErrorAndRejectionListeners\n    )\n    installProcessErrorHandlers(shouldRemoveUncaughtErrorAndRejectionListeners)\n  }\n\n  public async unstable_preloadEntries(): Promise<void> {\n    // Ensure prepare process will be finished before preloading entries.\n    await this.prepare()\n\n    const appPathsManifest = this.getAppPathsManifest()\n    const pagesManifest = this.getPagesManifest()\n\n    await this.loadCustomCacheHandlers()\n\n    for (const page of Object.keys(pagesManifest || {})) {\n      await loadComponents({\n        distDir: this.distDir,\n        page,\n        isAppPath: false,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      }).catch(() => {})\n    }\n\n    for (const page of Object.keys(appPathsManifest || {})) {\n      await loadComponents({\n        distDir: this.distDir,\n        page,\n        isAppPath: true,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      })\n        .then(async ({ ComponentMod }) => {\n          // we need to ensure fetch is patched before we require the page,\n          // otherwise if the fetch is patched by user code, we will be patching it\n          // too late and there won't be any caching behaviors\n          ComponentMod.patchFetch()\n\n          const webpackRequire = ComponentMod.__next_app__.require\n          if (webpackRequire?.m) {\n            for (const id of Object.keys(webpackRequire.m)) {\n              await webpackRequire(id)\n            }\n          }\n        })\n        .catch(() => {})\n    }\n  }\n\n  protected async handleUpgrade(): Promise<void> {\n    // The web server does not support web sockets, it's only used for HMR in\n    // development.\n  }\n\n  protected async loadInstrumentationModule() {\n    if (!this.serverOptions.dev) {\n      try {\n        this.instrumentation = await getInstrumentationModule(\n          this.dir,\n          this.nextConfig.distDir\n        )\n      } catch (err: any) {\n        if (err.code !== 'MODULE_NOT_FOUND') {\n          throw new Error(\n            'An error occurred while loading the instrumentation hook',\n            { cause: err }\n          )\n        }\n      }\n    }\n    return this.instrumentation\n  }\n\n  protected async prepareImpl() {\n    await super.prepareImpl()\n    await this.runInstrumentationHookIfAvailable()\n  }\n\n  protected async runInstrumentationHookIfAvailable() {\n    await ensureInstrumentationRegistered(this.dir, this.nextConfig.distDir)\n  }\n\n  protected loadEnvConfig({\n    dev,\n    forceReload,\n    silent,\n  }: {\n    dev: boolean\n    forceReload?: boolean\n    silent?: boolean\n  }) {\n    loadEnvConfig(\n      this.dir,\n      dev,\n      silent ? { info: () => {}, error: () => {} } : Log,\n      forceReload\n    )\n  }\n\n  private async loadCustomCacheHandlers() {\n    const { cacheHandlers } = this.nextConfig.experimental\n    if (!cacheHandlers) return\n\n    // If we've already initialized the cache handlers interface, don't do it\n    // again.\n    if (!initializeCacheHandlers()) return\n\n    for (const [kind, handler] of Object.entries(cacheHandlers)) {\n      if (!handler) continue\n\n      setCacheHandler(\n        kind,\n        interopDefault(\n          await dynamicImportEsmDefault(\n            formatDynamicImportPath(this.distDir, handler)\n          )\n        )\n      )\n    }\n  }\n\n  protected async getIncrementalCache({\n    requestHeaders,\n  }: {\n    requestHeaders: IncrementalCache['requestHeaders']\n  }) {\n    const dev = !!this.renderOpts.dev\n    let CacheHandler: any\n    const { cacheHandler } = this.nextConfig\n\n    if (cacheHandler) {\n      CacheHandler = interopDefault(\n        await dynamicImportEsmDefault(\n          formatDynamicImportPath(this.distDir, cacheHandler)\n        )\n      )\n    }\n\n    await this.loadCustomCacheHandlers()\n\n    // incremental-cache is request specific\n    // although can have shared caches in module scope\n    // per-cache handler\n    return new IncrementalCache({\n      fs: this.getCacheFilesystem(),\n      dev,\n      requestHeaders,\n      allowedRevalidateHeaderKeys:\n        this.nextConfig.experimental.allowedRevalidateHeaderKeys,\n      minimalMode: this.minimalMode,\n      serverDistDir: this.serverDistDir,\n      fetchCacheKeyPrefix: this.nextConfig.experimental.fetchCacheKeyPrefix,\n      maxMemoryCacheSize: this.nextConfig.cacheMaxMemorySize,\n      flushToDisk:\n        !this.minimalMode && this.nextConfig.experimental.isrFlushToDisk,\n      getPrerenderManifest: () => this.getPrerenderManifest(),\n      CurCacheHandler: CacheHandler,\n    })\n  }\n\n  protected getResponseCache() {\n    return new ResponseCache(this.minimalMode)\n  }\n\n  protected getPublicDir(): string {\n    return join(this.dir, CLIENT_PUBLIC_FILES_PATH)\n  }\n\n  protected getHasStaticDir(): boolean {\n    return fs.existsSync(join(this.dir, 'static'))\n  }\n\n  protected getPagesManifest(): PagesManifest | undefined {\n    return loadManifest(\n      join(this.serverDistDir, PAGES_MANIFEST)\n    ) as PagesManifest\n  }\n\n  protected getAppPathsManifest(): PagesManifest | undefined {\n    if (!this.enabledDirectories.app) return undefined\n\n    return loadManifest(\n      join(this.serverDistDir, APP_PATHS_MANIFEST)\n    ) as PagesManifest\n  }\n\n  protected getinterceptionRoutePatterns(): RegExp[] {\n    if (!this.enabledDirectories.app) return []\n\n    const routesManifest = this.getRoutesManifest()\n    return (\n      routesManifest?.rewrites.beforeFiles\n        .filter(isInterceptionRouteRewrite)\n        .map((rewrite) => new RegExp(rewrite.regex)) ?? []\n    )\n  }\n\n  protected async hasPage(pathname: string): Promise<boolean> {\n    return !!getMaybePagePath(\n      pathname,\n      this.distDir,\n      this.nextConfig.i18n?.locales,\n      this.enabledDirectories.app\n    )\n  }\n\n  protected getBuildId(): string {\n    const buildIdFile = join(this.distDir, BUILD_ID_FILE)\n    try {\n      return fs.readFileSync(buildIdFile, 'utf8').trim()\n    } catch (err: any) {\n      if (err.code === 'ENOENT') {\n        throw new Error(\n          `Could not find a production build in the '${this.distDir}' directory. Try building your app with 'next build' before starting the production server. https://nextjs.org/docs/messages/production-start-no-build-id`\n        )\n      }\n\n      throw err\n    }\n  }\n\n  protected getEnabledDirectories(dev: boolean): NextEnabledDirectories {\n    const dir = dev ? this.dir : this.serverDistDir\n\n    return {\n      app: findDir(dir, 'app') ? true : false,\n      pages: findDir(dir, 'pages') ? true : false,\n    }\n  }\n\n  protected sendRenderResult(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    options: {\n      result: RenderResult\n      type: 'html' | 'json' | 'rsc'\n      generateEtags: boolean\n      poweredByHeader: boolean\n      cacheControl: CacheControl | undefined\n    }\n  ): Promise<void> {\n    return sendRenderResult({\n      req: req.originalRequest,\n      res: res.originalResponse,\n      result: options.result,\n      type: options.type,\n      generateEtags: options.generateEtags,\n      poweredByHeader: options.poweredByHeader,\n      cacheControl: options.cacheControl,\n    })\n  }\n\n  protected async runApi(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean> {\n    const edgeFunctionsPages = this.getEdgeFunctionsPages()\n\n    for (const edgeFunctionsPage of edgeFunctionsPages) {\n      if (edgeFunctionsPage === match.definition.pathname) {\n        const handledAsEdgeFunction = await this.runEdgeFunction({\n          req,\n          res,\n          query,\n          params: match.params,\n          page: match.definition.pathname,\n          appPaths: null,\n        })\n\n        if (handledAsEdgeFunction) {\n          return true\n        }\n      }\n    }\n    // The module supports minimal mode, load the minimal module.\n    // Restore original URL as the handler handles it's own parsing\n    const parsedInitUrl = parseUrl(getRequestMeta(req, 'initURL') || req.url)\n    req.url = `${parsedInitUrl.pathname}${parsedInitUrl.search || ''}`\n\n    const loader = new NodeModuleLoader()\n    const module = (await loader.load(match.definition.filename)) as {\n      handler: (\n        req: IncomingMessage,\n        res: ServerResponse,\n        ctx: {\n          waitUntil: ReturnType<BaseServer['getWaitUntil']>\n        }\n      ) => Promise<void>\n    }\n    addRequestMeta(req.originalRequest, 'projectDir', this.dir)\n    addRequestMeta(req.originalRequest, 'distDir', this.distDir)\n    await module.handler(req.originalRequest, res.originalResponse, {\n      waitUntil: this.getWaitUntil(),\n    })\n    return true\n  }\n\n  protected async renderHTML(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult> {\n    return getTracer().trace(NextNodeServerSpan.renderHTML, async () =>\n      this.renderHTMLImpl(req, res, pathname, query, renderOpts)\n    )\n  }\n\n  private async renderHTMLImpl(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult> {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'Invariant: renderHTML should not be called in minimal mode'\n      )\n      // the `else` branch is needed for tree-shaking\n    } else {\n      // Due to the way we pass data by mutating `renderOpts`, we can't extend the\n      // object here but only updating its `nextFontManifest` field.\n      // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n      renderOpts.nextFontManifest = this.nextFontManifest\n\n      if (this.enabledDirectories.app && renderOpts.isAppPath) {\n        return lazyRenderAppPage(\n          req,\n          res,\n          pathname,\n          query,\n          // This code path does not service revalidations for unknown param\n          // shells. As a result, we don't need to pass in the unknown params.\n          null,\n          renderOpts,\n          this.getServerComponentsHmrCache(),\n          false,\n          {\n            buildId: this.buildId,\n          }\n        )\n      }\n\n      // TODO: re-enable this once we've refactored to use implicit matches\n      // throw new Error('Invariant: render should have used routeModule')\n\n      return lazyRenderPagesPage(\n        req.originalRequest,\n        res.originalResponse,\n        pathname,\n        query,\n        renderOpts,\n        {\n          buildId: this.buildId,\n          deploymentId: this.nextConfig.deploymentId,\n          customServer: this.serverOptions.customServer || undefined,\n        },\n        {\n          isFallback: false,\n          isDraftMode: renderOpts.isDraftMode,\n          developmentNotFoundSourcePage: getRequestMeta(\n            req,\n            'developmentNotFoundSourcePage'\n          ),\n        }\n      )\n    }\n  }\n\n  protected async imageOptimizer(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    paramsResult: import('./image-optimizer').ImageParamsResult,\n    previousCacheEntry?: IncrementalResponseCacheEntry | null\n  ): Promise<{\n    buffer: Buffer\n    contentType: string\n    maxAge: number\n    upstreamEtag: string\n    etag: string\n  }> {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'invariant: imageOptimizer should not be called in minimal mode'\n      )\n    } else {\n      const { imageOptimizer, fetchExternalImage, fetchInternalImage } =\n        require('./image-optimizer') as typeof import('./image-optimizer')\n\n      const handleInternalReq = async (\n        newReq: IncomingMessage,\n        newRes: ServerResponse\n      ) => {\n        if (newReq.url === req.url) {\n          throw new Error(`Invariant attempted to optimize _next/image itself`)\n        }\n\n        if (!this.routerServerHandler) {\n          throw new Error(`Invariant missing routerServerHandler`)\n        }\n\n        await this.routerServerHandler(newReq, newRes)\n        return\n      }\n\n      const { isAbsolute, href } = paramsResult\n\n      const imageUpstream = isAbsolute\n        ? await fetchExternalImage(href)\n        : await fetchInternalImage(\n            href,\n            req.originalRequest,\n            res.originalResponse,\n            handleInternalReq\n          )\n\n      return imageOptimizer(imageUpstream, paramsResult, this.nextConfig, {\n        isDev: this.renderOpts.dev,\n        previousCacheEntry,\n      })\n    }\n  }\n\n  protected getPagePath(pathname: string, locales?: string[]): string {\n    return getPagePath(\n      pathname,\n      this.distDir,\n      locales,\n      this.enabledDirectories.app\n    )\n  }\n\n  protected async renderPageComponent(\n    ctx: RequestContext<NodeNextRequest, NodeNextResponse>,\n    bubbleNoFallback: boolean\n  ) {\n    const edgeFunctionsPages = this.getEdgeFunctionsPages() || []\n    if (edgeFunctionsPages.length) {\n      const appPaths = this.getOriginalAppPaths(ctx.pathname)\n      const isAppPath = Array.isArray(appPaths)\n\n      let page = ctx.pathname\n      if (isAppPath) {\n        // When it's an array, we need to pass all parallel routes to the loader.\n        page = appPaths[0]\n      }\n\n      for (const edgeFunctionsPage of edgeFunctionsPages) {\n        if (edgeFunctionsPage === page) {\n          await this.runEdgeFunction({\n            req: ctx.req,\n            res: ctx.res,\n            query: ctx.query,\n            params: ctx.renderOpts.params,\n            page,\n            appPaths,\n          })\n          return null\n        }\n      }\n    }\n\n    return super.renderPageComponent(ctx, bubbleNoFallback)\n  }\n\n  protected async findPageComponents({\n    locale,\n    page,\n    query,\n    params,\n    isAppPath,\n    url,\n  }: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    // The following parameters are used in the development server's\n    // implementation.\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null> {\n    return getTracer().trace(\n      NextNodeServerSpan.findPageComponents,\n      {\n        spanName: 'resolve page components',\n        attributes: {\n          'next.route': isAppPath ? normalizeAppPath(page) : page,\n        },\n      },\n      () =>\n        this.findPageComponentsImpl({\n          locale,\n          page,\n          query,\n          params,\n          isAppPath,\n          url,\n        })\n    )\n  }\n\n  private async findPageComponentsImpl({\n    locale,\n    page,\n    query,\n    params,\n    isAppPath,\n    url: _url,\n  }: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null> {\n    const pagePaths: string[] = [page]\n    if (query.amp) {\n      // try serving a static AMP version first\n      pagePaths.unshift(\n        (isAppPath ? normalizeAppPath(page) : normalizePagePath(page)) + '.amp'\n      )\n    }\n\n    if (locale) {\n      pagePaths.unshift(\n        ...pagePaths.map((path) => `/${locale}${path === '/' ? '' : path}`)\n      )\n    }\n\n    for (const pagePath of pagePaths) {\n      try {\n        const components = await loadComponents({\n          distDir: this.distDir,\n          page: pagePath,\n          isAppPath,\n          isDev: this.isDev,\n          sriEnabled: this.sriEnabled,\n        })\n\n        if (\n          locale &&\n          typeof components.Component === 'string' &&\n          !pagePath.startsWith(`/${locale}/`) &&\n          pagePath !== `/${locale}`\n        ) {\n          // if loading an static HTML file the locale is required\n          // to be present since all HTML files are output under their locale\n          continue\n        }\n\n        return {\n          components,\n          query: {\n            ...(!this.renderOpts.isExperimentalCompile &&\n            components.getStaticProps\n              ? ({\n                  amp: query.amp,\n                } as NextParsedUrlQuery)\n              : query),\n            // For appDir params is excluded.\n            ...((isAppPath ? {} : params) || {}),\n          },\n        }\n      } catch (err) {\n        // we should only not throw if we failed to find the page\n        // in the pages-manifest\n        if (!(err instanceof PageNotFoundError)) {\n          throw err\n        }\n      }\n    }\n    return null\n  }\n\n  protected getNextFontManifest(): NextFontManifest | undefined {\n    return loadManifest(\n      join(this.distDir, 'server', NEXT_FONT_MANIFEST + '.json')\n    ) as NextFontManifest\n  }\n\n  protected handleNextImageRequest: NodeRouteHandler = async (\n    req,\n    res,\n    parsedUrl\n  ) => {\n    if (!parsedUrl.pathname || !parsedUrl.pathname.startsWith('/_next/image')) {\n      return false\n    }\n    // Ignore if its a middleware request\n    if (getRequestMeta(req, 'middlewareInvoke')) {\n      return false\n    }\n\n    if (\n      this.minimalMode ||\n      this.nextConfig.output === 'export' ||\n      process.env.NEXT_MINIMAL\n    ) {\n      res.statusCode = 400\n      res.body('Bad Request').send()\n      return true\n      // the `else` branch is needed for tree-shaking\n    } else {\n      const { ImageOptimizerCache } =\n        require('./image-optimizer') as typeof import('./image-optimizer')\n\n      const imageOptimizerCache = new ImageOptimizerCache({\n        distDir: this.distDir,\n        nextConfig: this.nextConfig,\n      })\n\n      const { sendResponse, ImageError } =\n        require('./image-optimizer') as typeof import('./image-optimizer')\n\n      if (!this.imageResponseCache) {\n        throw new Error('invariant image optimizer cache was not initialized')\n      }\n      const imagesConfig = this.nextConfig.images\n\n      if (imagesConfig.loader !== 'default' || imagesConfig.unoptimized) {\n        await this.render404(req, res)\n        return true\n      }\n\n      const paramsResult = ImageOptimizerCache.validateParams(\n        req.originalRequest,\n        parsedUrl.query,\n        this.nextConfig,\n        !!this.renderOpts.dev\n      )\n\n      if ('errorMessage' in paramsResult) {\n        res.statusCode = 400\n        res.body(paramsResult.errorMessage).send()\n        return true\n      }\n\n      const cacheKey = ImageOptimizerCache.getCacheKey(paramsResult)\n\n      try {\n        const { getExtension } =\n          require('./serve-static') as typeof import('./serve-static')\n        const cacheEntry = await this.imageResponseCache.get(\n          cacheKey,\n          async ({ previousCacheEntry }) => {\n            const { buffer, contentType, maxAge, upstreamEtag, etag } =\n              await this.imageOptimizer(\n                req,\n                res,\n                paramsResult,\n                previousCacheEntry\n              )\n\n            return {\n              value: {\n                kind: CachedRouteKind.IMAGE,\n                buffer,\n                etag,\n                extension: getExtension(contentType) as string,\n                upstreamEtag,\n              },\n              cacheControl: { revalidate: maxAge, expire: undefined },\n            }\n          },\n          {\n            routeKind: RouteKind.IMAGE,\n            incrementalCache: imageOptimizerCache,\n            isFallback: false,\n          }\n        )\n\n        if (cacheEntry?.value?.kind !== CachedRouteKind.IMAGE) {\n          throw new Error(\n            'invariant did not get entry from image response cache'\n          )\n        }\n\n        sendResponse(\n          req.originalRequest,\n          res.originalResponse,\n          paramsResult.href,\n          cacheEntry.value.extension,\n          cacheEntry.value.buffer,\n          cacheEntry.value.etag,\n          paramsResult.isStatic,\n          cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT',\n          imagesConfig,\n          cacheEntry.cacheControl?.revalidate || 0,\n          Boolean(this.renderOpts.dev)\n        )\n        return true\n      } catch (err) {\n        if (err instanceof ImageError) {\n          res.statusCode = err.statusCode\n          res.body(err.message).send()\n          return true\n        }\n        throw err\n      }\n    }\n  }\n\n  protected handleCatchallRenderRequest: NodeRouteHandler = async (\n    req,\n    res,\n    parsedUrl\n  ) => {\n    let { pathname, query } = parsedUrl\n    if (!pathname) {\n      throw new Error('Invariant: pathname is undefined')\n    }\n\n    // This is a catch-all route, there should be no fallbacks so mark it as\n    // such.\n    addRequestMeta(req, 'bubbleNoFallback', true)\n\n    // TODO: this is only needed until route-module can handle\n    // rendering/serving the 404 directly with next-server\n    if (!routerServerGlobal[RouterServerContextSymbol]) {\n      routerServerGlobal[RouterServerContextSymbol] = {}\n    }\n    const relativeProjectDir = relative(process.cwd(), this.dir)\n    const existingServerContext =\n      routerServerGlobal[RouterServerContextSymbol][relativeProjectDir]\n\n    if (!existingServerContext) {\n      routerServerGlobal[RouterServerContextSymbol][relativeProjectDir] = {\n        render404: this.render404.bind(this),\n      }\n    }\n    routerServerGlobal[RouterServerContextSymbol][\n      relativeProjectDir\n    ].nextConfig = this.nextConfig\n\n    try {\n      // next.js core assumes page path without trailing slash\n      pathname = removeTrailingSlash(pathname)\n\n      const options: MatchOptions = {\n        i18n: this.i18nProvider?.fromRequest(req, pathname),\n      }\n      const match = await this.matchers.match(pathname, options)\n\n      // If we don't have a match, try to render it anyways.\n      if (!match) {\n        await this.render(req, res, pathname, query, parsedUrl, true)\n\n        return true\n      }\n\n      // Add the match to the request so we don't have to re-run the matcher\n      // for the same request.\n      addRequestMeta(req, 'match', match)\n\n      // TODO-APP: move this to a route handler\n      const edgeFunctionsPages = this.getEdgeFunctionsPages()\n      for (const edgeFunctionsPage of edgeFunctionsPages) {\n        // If the page doesn't match the edge function page, skip it.\n        if (edgeFunctionsPage !== match.definition.page) continue\n\n        if (this.nextConfig.output === 'export') {\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n        delete query[NEXT_RSC_UNION_QUERY]\n\n        // If we handled the request, we can return early.\n        // For api routes edge runtime\n        try {\n          const handled = await this.runEdgeFunction({\n            req,\n            res,\n            query,\n            params: match.params,\n            page: match.definition.page,\n            match,\n            appPaths: null,\n          })\n          if (handled) return true\n        } catch (apiError) {\n          await this.instrumentationOnRequestError(apiError, req, {\n            routePath: match.definition.page,\n            routerKind: 'Pages Router',\n            routeType: 'route',\n            // Edge runtime does not support ISR\n            revalidateReason: undefined,\n          })\n          throw apiError\n        }\n      }\n\n      // If the route was detected as being a Pages API route, then handle\n      // it.\n      // TODO: move this behavior into a route handler.\n      if (isPagesAPIRouteMatch(match)) {\n        if (this.nextConfig.output === 'export') {\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n\n        const handled = await this.handleApiRequest(req, res, query, match)\n        if (handled) return true\n      }\n\n      await this.render(req, res, pathname, query, parsedUrl, true)\n\n      return true\n    } catch (err: any) {\n      if (err instanceof NoFallbackError) {\n        throw err\n      }\n\n      try {\n        if (this.renderOpts.dev) {\n          const { formatServerError } =\n            require('../lib/format-server-error') as typeof import('../lib/format-server-error')\n          formatServerError(err)\n          this.logErrorWithOriginalStack(err)\n        } else {\n          this.logError(err)\n        }\n        res.statusCode = 500\n        await this.renderError(err, req, res, pathname, query)\n        return true\n      } catch {}\n\n      throw err\n    }\n  }\n\n  // Used in development only, overloaded in next-dev-server\n  protected logErrorWithOriginalStack(\n    _err?: unknown,\n    _type?: 'unhandledRejection' | 'uncaughtException' | 'warning' | 'app-dir'\n  ): void {\n    throw new Error(\n      'Invariant: logErrorWithOriginalStack can only be called on the development server'\n    )\n  }\n\n  // Used in development only, overloaded in next-dev-server\n  protected async ensurePage(_opts: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    match?: RouteMatch\n    url?: string\n  }): Promise<void> {\n    throw new Error(\n      'Invariant: ensurePage can only be called on the development server'\n    )\n  }\n\n  /**\n   * Resolves `API` request, in development builds on demand\n   * @param req http request\n   * @param res http response\n   * @param pathname path of request\n   */\n  protected async handleApiRequest(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean> {\n    return this.runApi(req, res, query, match)\n  }\n\n  protected getCacheFilesystem(): CacheFs {\n    return nodeFs\n  }\n\n  protected normalizeReq(\n    req: NodeNextRequest | IncomingMessage\n  ): NodeNextRequest {\n    return !(req instanceof NodeNextRequest) ? new NodeNextRequest(req) : req\n  }\n\n  protected normalizeRes(\n    res: NodeNextResponse | ServerResponse\n  ): NodeNextResponse {\n    return !(res instanceof NodeNextResponse) ? new NodeNextResponse(res) : res\n  }\n\n  public getRequestHandler(): NodeRequestHandler {\n    const handler = this.makeRequestHandler()\n    if (this.serverOptions.experimentalTestProxy) {\n      const { wrapRequestHandlerNode } =\n        // eslint-disable-next-line @next/internal/typechecked-require -- experimental/testmode is not built ins next/dist/esm\n        require('next/dist/experimental/testmode/server') as typeof import('../experimental/testmode/server')\n      return wrapRequestHandlerNode(handler)\n    }\n    return handler\n  }\n\n  private makeRequestHandler(): NodeRequestHandler {\n    // This is just optimization to fire prepare as soon as possible. It will be\n    // properly awaited later. We add the catch here to ensure that it does not\n    // cause an unhandled promise rejection. The promise rejection will be\n    // handled later on via the `await` when the request handler is called.\n    this.prepare().catch((err) => {\n      console.error('Failed to prepare server', err)\n    })\n\n    const handler = super.getRequestHandler()\n\n    return (req, res, parsedUrl) =>\n      handler(this.normalizeReq(req), this.normalizeRes(res), parsedUrl)\n  }\n\n  public async revalidate({\n    urlPath,\n    revalidateHeaders,\n    opts,\n  }: {\n    urlPath: string\n    revalidateHeaders: { [key: string]: string | string[] }\n    opts: { unstable_onlyGenerated?: boolean }\n  }) {\n    const mocked = createRequestResponseMocks({\n      url: urlPath,\n      headers: revalidateHeaders,\n    })\n\n    const handler = this.getRequestHandler()\n    await handler(\n      new NodeNextRequest(mocked.req),\n      new NodeNextResponse(mocked.res)\n    )\n    await mocked.res.hasStreamed\n\n    if (\n      mocked.res.getHeader('x-nextjs-cache') !== 'REVALIDATED' &&\n      mocked.res.statusCode !== 200 &&\n      !(mocked.res.statusCode === 404 && opts.unstable_onlyGenerated)\n    ) {\n      throw new Error(`Invalid response ${mocked.res.statusCode}`)\n    }\n  }\n\n  public async render(\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: NextParsedUrlQuery,\n    parsedUrl?: NextUrlWithParsedQuery,\n    internal = false\n  ): Promise<void> {\n    return super.render(\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query,\n      parsedUrl,\n      internal\n    )\n  }\n\n  public async renderToHTML(\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: ParsedUrlQuery\n  ): Promise<string | null> {\n    return super.renderToHTML(\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query\n    )\n  }\n\n  protected async renderErrorToResponseImpl(\n    ctx: RequestContext<NodeNextRequest, NodeNextResponse>,\n    err: Error | null\n  ) {\n    const { req, res, query } = ctx\n    const is404 = res.statusCode === 404\n\n    if (is404 && this.enabledDirectories.app) {\n      if (this.renderOpts.dev) {\n        await this.ensurePage({\n          page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n          clientOnly: false,\n          url: req.url,\n        }).catch(() => {})\n      }\n\n      if (\n        this.getEdgeFunctionsPages().includes(UNDERSCORE_NOT_FOUND_ROUTE_ENTRY)\n      ) {\n        await this.runEdgeFunction({\n          req,\n          res,\n          query: query || {},\n          params: {},\n          page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n          appPaths: null,\n        })\n        return null\n      }\n    }\n    return super.renderErrorToResponseImpl(ctx, err)\n  }\n\n  public async renderError(\n    err: Error | null,\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: NextParsedUrlQuery,\n    setHeaders?: boolean\n  ): Promise<void> {\n    return super.renderError(\n      err,\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query,\n      setHeaders\n    )\n  }\n\n  public async renderErrorToHTML(\n    err: Error | null,\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: ParsedUrlQuery\n  ): Promise<string | null> {\n    return super.renderErrorToHTML(\n      err,\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query\n    )\n  }\n\n  public async render404(\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery,\n    setHeaders?: boolean\n  ): Promise<void> {\n    return super.render404(\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      parsedUrl,\n      setHeaders\n    )\n  }\n\n  protected getMiddlewareManifest(): MiddlewareManifest | null {\n    if (this.minimalMode) {\n      return null\n    } else {\n      const manifest: MiddlewareManifest = require(this.middlewareManifestPath)\n      return manifest\n    }\n  }\n\n  /** Returns the middleware routing item if there is one. */\n  protected async getMiddleware(): Promise<MiddlewareRoutingItem | undefined> {\n    const manifest = this.getMiddlewareManifest()\n    const middleware = manifest?.middleware?.['/']\n    if (!middleware) {\n      const middlewareModule = await this.loadNodeMiddleware()\n\n      if (middlewareModule) {\n        return {\n          match: getMiddlewareRouteMatcher(\n            middlewareModule.config?.matchers || [\n              { regexp: '.*', originalSource: '/:path*' },\n            ]\n          ),\n          page: '/',\n        }\n      }\n\n      return\n    }\n\n    return {\n      match: getMiddlewareMatcher(middleware),\n      page: '/',\n    }\n  }\n\n  protected getEdgeFunctionsPages(): string[] {\n    const manifest = this.getMiddlewareManifest()\n    if (!manifest) {\n      return []\n    }\n\n    return Object.keys(manifest.functions)\n  }\n\n  /**\n   * Get information for the edge function located in the provided page\n   * folder. If the edge function info can't be found it will throw\n   * an error.\n   */\n  protected getEdgeFunctionInfo(params: {\n    page: string\n    /** Whether we should look for a middleware or not */\n    middleware: boolean\n  }): {\n    name: string\n    paths: string[]\n    wasm: { filePath: string; name: string }[]\n    env: { [key: string]: string }\n    assets?: { filePath: string; name: string }[]\n  } | null {\n    const manifest = this.getMiddlewareManifest()\n    if (!manifest) {\n      return null\n    }\n\n    let foundPage: string\n\n    try {\n      foundPage = denormalizePagePath(normalizePagePath(params.page))\n    } catch (err) {\n      return null\n    }\n\n    let pageInfo = params.middleware\n      ? manifest.middleware[foundPage]\n      : manifest.functions[foundPage]\n\n    if (!pageInfo) {\n      if (!params.middleware) {\n        throw new PageNotFoundError(foundPage)\n      }\n      return null\n    }\n\n    return {\n      name: pageInfo.name,\n      paths: pageInfo.files.map((file) => join(this.distDir, file)),\n      wasm: (pageInfo.wasm ?? []).map((binding) => ({\n        ...binding,\n        filePath: join(this.distDir, binding.filePath),\n      })),\n      assets:\n        pageInfo.assets &&\n        pageInfo.assets.map((binding) => {\n          return {\n            ...binding,\n            filePath: join(this.distDir, binding.filePath),\n          }\n        }),\n      env: pageInfo.env,\n    }\n  }\n\n  private async loadNodeMiddleware() {\n    if (!process.env.NEXT_MINIMAL) {\n      if (!this.nextConfig.experimental.nodeMiddleware) {\n        return\n      }\n\n      try {\n        const functionsConfig = this.renderOpts.dev\n          ? {}\n          : require(join(this.distDir, 'server', FUNCTIONS_CONFIG_MANIFEST))\n\n        if (\n          this.renderOpts.dev ||\n          functionsConfig?.functions?.['/_middleware']\n        ) {\n          // if used with top level await, this will be a promise\n          return require(join(this.distDir, 'server', 'middleware.js'))\n        }\n      } catch (err) {\n        if (\n          isError(err) &&\n          err.code !== 'ENOENT' &&\n          err.code !== 'MODULE_NOT_FOUND'\n        ) {\n          throw err\n        }\n      }\n    }\n  }\n\n  /**\n   * Checks if a middleware exists. This method is useful for the development\n   * server where we need to check the filesystem. Here we just check the\n   * middleware manifest.\n   */\n  protected async hasMiddleware(pathname: string): Promise<boolean> {\n    const info = this.getEdgeFunctionInfo({ page: pathname, middleware: true })\n    const nodeMiddleware = await this.loadNodeMiddleware()\n\n    if (!info && nodeMiddleware) {\n      return true\n    }\n    return Boolean(info && info.paths.length > 0)\n  }\n\n  /**\n   * A placeholder for a function to be defined in the development server.\n   * It will make sure that the root middleware or an edge function has been compiled\n   * so that we can run it.\n   */\n  protected async ensureMiddleware(_url?: string) {}\n  protected async ensureEdgeFunction(_params: {\n    page: string\n    appPaths: string[] | null\n    url?: string\n  }) {}\n\n  /**\n   * This method gets all middleware matchers and execute them when the request\n   * matches. It will make sure that each middleware exists and is compiled and\n   * ready to be invoked. The development server will decorate it to add warns\n   * and errors with rich traces.\n   */\n  protected async runMiddleware(params: {\n    request: NodeNextRequest\n    response: NodeNextResponse\n    parsedUrl: ParsedUrl\n    parsed: UrlWithParsedQuery\n    onWarning?: (warning: Error) => void\n  }) {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'invariant: runMiddleware should not be called in minimal mode'\n      )\n    }\n\n    // Middleware is skipped for on-demand revalidate requests\n    if (\n      checkIsOnDemandRevalidate(params.request, this.renderOpts.previewProps)\n        .isOnDemandRevalidate\n    ) {\n      return {\n        response: new Response(null, { headers: { 'x-middleware-next': '1' } }),\n      } as FetchEventResult\n    }\n\n    let url: string\n\n    if (this.nextConfig.skipMiddlewareUrlNormalize) {\n      url = getRequestMeta(params.request, 'initURL')!\n    } else {\n      // For middleware to \"fetch\" we must always provide an absolute URL\n      const query = urlQueryToSearchParams(params.parsed.query).toString()\n      const locale = getRequestMeta(params.request, 'locale')\n\n      url = `${getRequestMeta(params.request, 'initProtocol')}://${\n        this.fetchHostname || 'localhost'\n      }:${this.port}${locale ? `/${locale}` : ''}${params.parsed.pathname}${\n        query ? `?${query}` : ''\n      }`\n    }\n\n    if (!url.startsWith('http')) {\n      throw new Error(\n        'To use middleware you must provide a `hostname` and `port` to the Next.js Server'\n      )\n    }\n\n    const page: {\n      name?: string\n      params?: { [key: string]: string | string[] }\n    } = {}\n\n    const middleware = await this.getMiddleware()\n    if (!middleware) {\n      return { finished: false }\n    }\n    if (!(await this.hasMiddleware(middleware.page))) {\n      return { finished: false }\n    }\n\n    await this.ensureMiddleware(params.request.url)\n    const middlewareInfo = this.getEdgeFunctionInfo({\n      page: middleware.page,\n      middleware: true,\n    })\n\n    const method = (params.request.method || 'GET').toUpperCase()\n    const requestData = {\n      headers: params.request.headers,\n      method,\n      nextConfig: {\n        basePath: this.nextConfig.basePath,\n        i18n: this.nextConfig.i18n,\n        trailingSlash: this.nextConfig.trailingSlash,\n        experimental: this.nextConfig.experimental,\n      },\n      url: url,\n      page,\n      body:\n        method !== 'GET' && method !== 'HEAD'\n          ? (getRequestMeta(params.request, 'clonableBody') as any)\n          : undefined,\n\n      signal: signalFromNodeResponse(params.response.originalResponse),\n      waitUntil: this.getWaitUntil(),\n    }\n    let result:\n      | UnwrapPromise<ReturnType<typeof import('./web/sandbox').run>>\n      | undefined\n\n    // if no middleware info check for Node.js middleware\n    // this is not in the middleware-manifest as that historically\n    // has only included edge-functions, we need to do a breaking\n    // version bump for that manifest to write this info there if\n    // we decide we want to\n    if (!middlewareInfo) {\n      let middlewareModule\n      middlewareModule = await this.loadNodeMiddleware()\n\n      if (!middlewareModule) {\n        throw new MiddlewareNotFoundError()\n      }\n      const adapterFn: typeof import('./web/adapter').adapter =\n        middlewareModule.default || middlewareModule\n\n      const hasRequestBody =\n        !['HEAD', 'GET'].includes(params.request.method) &&\n        Boolean(requestData.body)\n\n      try {\n        result = await adapterFn({\n          handler: middlewareModule.middleware || middlewareModule,\n          request: {\n            ...requestData,\n            body: hasRequestBody\n              ? requestData.body.cloneBodyStream()\n              : undefined,\n          },\n          page: 'middleware',\n        })\n      } finally {\n        if (hasRequestBody) {\n          requestData.body.finalize()\n        }\n      }\n    } else {\n      const { run } = require('./web/sandbox') as typeof import('./web/sandbox')\n\n      result = await run({\n        distDir: this.distDir,\n        name: middlewareInfo.name,\n        paths: middlewareInfo.paths,\n        edgeFunctionEntry: middlewareInfo,\n        request: requestData,\n        useCache: true,\n        onWarning: params.onWarning,\n      })\n    }\n\n    if (!this.renderOpts.dev) {\n      result.waitUntil.catch((error) => {\n        console.error(`Uncaught: middleware waitUntil errored`, error)\n      })\n    }\n\n    if (!result) {\n      this.render404(params.request, params.response, params.parsed)\n      return { finished: true }\n    }\n\n    // Split compound (comma-separated) set-cookie headers\n    if (result.response.headers.has('set-cookie')) {\n      const cookies = result.response.headers\n        .getSetCookie()\n        .flatMap((maybeCompoundCookie) =>\n          splitCookiesString(maybeCompoundCookie)\n        )\n\n      // Clear existing header(s)\n      result.response.headers.delete('set-cookie')\n\n      // Append each cookie individually.\n      for (const cookie of cookies) {\n        result.response.headers.append('set-cookie', cookie)\n      }\n\n      // Add cookies to request meta.\n      addRequestMeta(params.request, 'middlewareCookie', cookies)\n    }\n\n    return result\n  }\n\n  protected handleCatchallMiddlewareRequest: NodeRouteHandler = async (\n    req,\n    res,\n    parsed\n  ) => {\n    const isMiddlewareInvoke = getRequestMeta(req, 'middlewareInvoke')\n\n    if (!isMiddlewareInvoke) {\n      return false\n    }\n\n    const handleFinished = () => {\n      addRequestMeta(req, 'middlewareInvoke', true)\n      res.body('').send()\n      return true\n    }\n\n    const middleware = await this.getMiddleware()\n    if (!middleware) {\n      return handleFinished()\n    }\n\n    const initUrl = getRequestMeta(req, 'initURL')!\n    const parsedUrl = parseUrl(initUrl)\n    const pathnameInfo = getNextPathnameInfo(parsedUrl.pathname, {\n      nextConfig: this.nextConfig,\n      i18nProvider: this.i18nProvider,\n    })\n\n    parsedUrl.pathname = pathnameInfo.pathname\n    const normalizedPathname = removeTrailingSlash(parsed.pathname || '')\n    let maybeDecodedPathname = normalizedPathname\n\n    try {\n      maybeDecodedPathname = decodeURIComponent(normalizedPathname)\n    } catch {\n      /* non-fatal we can't decode so can't match it */\n    }\n\n    if (\n      !(\n        middleware.match(normalizedPathname, req, parsedUrl.query) ||\n        middleware.match(maybeDecodedPathname, req, parsedUrl.query)\n      )\n    ) {\n      return handleFinished()\n    }\n\n    let result: Awaited<\n      ReturnType<typeof NextNodeServer.prototype.runMiddleware>\n    >\n    let bubblingResult = false\n\n    try {\n      await this.ensureMiddleware(req.url)\n\n      result = await this.runMiddleware({\n        request: req,\n        response: res,\n        parsedUrl: parsedUrl,\n        parsed: parsed,\n      })\n\n      if ('response' in result) {\n        if (isMiddlewareInvoke) {\n          bubblingResult = true\n          throw new BubbledError(true, result)\n        }\n\n        for (const [key, value] of Object.entries(\n          toNodeOutgoingHttpHeaders(result.response.headers)\n        )) {\n          if (key !== 'content-encoding' && value !== undefined) {\n            res.setHeader(key, value as string | string[])\n          }\n        }\n        res.statusCode = result.response.status\n\n        const { originalResponse } = res\n        if (result.response.body) {\n          await pipeToNodeResponse(result.response.body, originalResponse)\n        } else {\n          originalResponse.end()\n        }\n        return true\n      }\n    } catch (err: unknown) {\n      if (bubblingResult) {\n        throw err\n      }\n\n      if (isError(err) && err.code === 'ENOENT') {\n        await this.render404(req, res, parsed)\n        return true\n      }\n\n      if (err instanceof DecodeError) {\n        res.statusCode = 400\n        await this.renderError(err, req, res, parsed.pathname || '')\n        return true\n      }\n\n      const error = getProperError(err)\n      console.error(error)\n      res.statusCode = 500\n      await this.renderError(error, req, res, parsed.pathname || '')\n      return true\n    }\n\n    return result.finished\n  }\n\n  private _cachedPreviewManifest: PrerenderManifest | undefined\n  protected getPrerenderManifest(): PrerenderManifest {\n    if (this._cachedPreviewManifest) {\n      return this._cachedPreviewManifest\n    }\n\n    this._cachedPreviewManifest = loadManifest(\n      join(this.distDir, PRERENDER_MANIFEST)\n    ) as PrerenderManifest\n\n    return this._cachedPreviewManifest\n  }\n\n  protected getRoutesManifest(): NormalizedRouteManifest | undefined {\n    return getTracer().trace(\n      NextNodeServerSpan.getRoutesManifest,\n      () => loadManifest(join(this.distDir, ROUTES_MANIFEST)) as RoutesManifest\n    )\n  }\n\n  protected attachRequestMeta(\n    req: NodeNextRequest,\n    parsedUrl: NextUrlWithParsedQuery,\n    isUpgradeReq?: boolean\n  ) {\n    // Injected in base-server.ts\n    const protocol = req.headers['x-forwarded-proto']?.includes('https')\n      ? 'https'\n      : 'http'\n\n    // When there are hostname and port we build an absolute URL\n    const initUrl =\n      this.fetchHostname && this.port\n        ? `${protocol}://${this.fetchHostname}:${this.port}${req.url}`\n        : this.nextConfig.experimental.trustHostHeader\n          ? `https://${req.headers.host || 'localhost'}${req.url}`\n          : req.url\n\n    addRequestMeta(req, 'initURL', initUrl)\n    addRequestMeta(req, 'initQuery', { ...parsedUrl.query })\n    addRequestMeta(req, 'initProtocol', protocol)\n\n    if (!isUpgradeReq) {\n      addRequestMeta(req, 'clonableBody', getCloneableBody(req.originalRequest))\n    }\n  }\n\n  protected async runEdgeFunction(params: {\n    req: NodeNextRequest\n    res: NodeNextResponse\n    query: ParsedUrlQuery\n    params: Params | undefined\n    page: string\n    appPaths: string[] | null\n    match?: RouteMatch\n    onError?: (err: unknown) => void\n    onWarning?: (warning: Error) => void\n  }): Promise<FetchEventResult | null> {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'Middleware is not supported in minimal mode. Please remove the `NEXT_MINIMAL` environment variable.'\n      )\n    }\n    let edgeInfo: ReturnType<typeof this.getEdgeFunctionInfo> | undefined\n\n    const { query, page, match } = params\n\n    if (!match)\n      await this.ensureEdgeFunction({\n        page,\n        appPaths: params.appPaths,\n        url: params.req.url,\n      })\n    edgeInfo = this.getEdgeFunctionInfo({\n      page,\n      middleware: false,\n    })\n\n    if (!edgeInfo) {\n      return null\n    }\n\n    // For edge to \"fetch\" we must always provide an absolute URL\n    const isNextDataRequest = getRequestMeta(params.req, 'isNextDataReq')\n    const initialUrl = new URL(\n      getRequestMeta(params.req, 'initURL') || '/',\n      'http://n'\n    )\n    const queryString = urlQueryToSearchParams({\n      ...Object.fromEntries(initialUrl.searchParams),\n      ...query,\n      ...params.params,\n    }).toString()\n\n    if (isNextDataRequest) {\n      params.req.headers['x-nextjs-data'] = '1'\n    }\n    initialUrl.search = queryString\n    const url = initialUrl.toString()\n\n    if (!url.startsWith('http')) {\n      throw new Error(\n        'To use middleware you must provide a `hostname` and `port` to the Next.js Server'\n      )\n    }\n\n    const { run } = require('./web/sandbox') as typeof import('./web/sandbox')\n    const result = await run({\n      distDir: this.distDir,\n      name: edgeInfo.name,\n      paths: edgeInfo.paths,\n      edgeFunctionEntry: edgeInfo,\n      request: {\n        headers: params.req.headers,\n        method: params.req.method,\n        nextConfig: {\n          basePath: this.nextConfig.basePath,\n          i18n: this.nextConfig.i18n,\n          trailingSlash: this.nextConfig.trailingSlash,\n        },\n        url,\n        page: {\n          name: params.page,\n          ...(params.params && { params: params.params }),\n        },\n        body: getRequestMeta(params.req, 'clonableBody'),\n        signal: signalFromNodeResponse(params.res.originalResponse),\n        waitUntil: this.getWaitUntil(),\n      },\n      useCache: true,\n      onError: params.onError,\n      onWarning: params.onWarning,\n      incrementalCache:\n        (globalThis as any).__incrementalCache ||\n        getRequestMeta(params.req, 'incrementalCache'),\n      serverComponentsHmrCache: getRequestMeta(\n        params.req,\n        'serverComponentsHmrCache'\n      ),\n    })\n\n    if (result.fetchMetrics) {\n      params.req.fetchMetrics = result.fetchMetrics\n    }\n\n    if (!params.res.statusCode || params.res.statusCode < 400) {\n      params.res.statusCode = result.response.status\n      params.res.statusMessage = result.response.statusText\n    }\n\n    // TODO: (wyattjoh) investigate improving this\n\n    result.response.headers.forEach((value, key) => {\n      // The append handling is special cased for `set-cookie`.\n      if (key.toLowerCase() === 'set-cookie') {\n        // TODO: (wyattjoh) replace with native response iteration when we can upgrade undici\n        for (const cookie of splitCookiesString(value)) {\n          params.res.appendHeader(key, cookie)\n        }\n      } else {\n        params.res.appendHeader(key, value)\n      }\n    })\n\n    const { originalResponse } = params.res\n    if (result.response.body) {\n      await pipeToNodeResponse(result.response.body, originalResponse)\n    } else {\n      originalResponse.end()\n    }\n\n    return result\n  }\n\n  protected get serverDistDir(): string {\n    if (this._serverDistDir) {\n      return this._serverDistDir\n    }\n    const serverDistDir = join(this.distDir, SERVER_DIRECTORY)\n    this._serverDistDir = serverDistDir\n    return serverDistDir\n  }\n\n  protected async getFallbackErrorComponents(\n    _url?: string\n  ): Promise<LoadComponentsReturnType | null> {\n    // Not implemented for production use cases, this is implemented on the\n    // development server.\n    return null\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    await super.instrumentationOnRequestError(...args)\n\n    // For Node.js runtime production logs, in dev it will be overridden by next-dev-server\n    if (!this.renderOpts.dev) {\n      this.logError(args[0] as Error)\n    }\n  }\n\n  protected onServerClose(listener: () => Promise<void>) {\n    this.cleanupListeners.add(listener)\n  }\n\n  async close(): Promise<void> {\n    await this.cleanupListeners.runAll()\n  }\n\n  protected getInternalWaitUntil(): WaitUntil {\n    this.internalWaitUntil ??= this.createInternalWaitUntil()\n    return this.internalWaitUntil\n  }\n\n  private createInternalWaitUntil() {\n    if (this.minimalMode) {\n      throw new InvariantError(\n        'createInternalWaitUntil should never be called in minimal mode'\n      )\n    }\n\n    const awaiter = new AwaiterOnce({ onError: console.error })\n\n    // TODO(after): warn if the process exits before these are awaited\n    this.onServerClose(() => awaiter.awaiting())\n\n    return awaiter.waitUntil\n  }\n}\n"], "names": ["NextNodeServer", "dynamicImportEsmDefault", "process", "env", "NEXT_MINIMAL", "id", "then", "mod", "default", "MiddlewareMatcherCache", "WeakMap", "getMiddlewareMatcher", "info", "stored", "get", "Array", "isArray", "matchers", "Error", "JSON", "stringify", "matcher", "getMiddlewareRouteMatcher", "set", "installProcessErrorHandlers", "shouldRemoveUncaughtErrorAndRejectionListeners", "removeAllListeners", "on", "reason", "isPostpone", "console", "error", "BaseServer", "constructor", "options", "registeredInstrumentation", "cleanupListeners", "AsyncCallbackSet", "handleNextImageRequest", "req", "res", "parsedUrl", "pathname", "startsWith", "getRequestMeta", "minimalMode", "nextConfig", "output", "statusCode", "body", "send", "ImageOptimizerCache", "require", "imageOptimizerCache", "distDir", "sendResponse", "ImageError", "imageResponseCache", "imagesConfig", "images", "loader", "unoptimized", "render404", "paramsResult", "validateParams", "originalRequest", "query", "renderOpts", "dev", "errorMessage", "cache<PERSON>ey", "get<PERSON><PERSON><PERSON><PERSON>", "cacheEntry", "getExtension", "previousCacheEntry", "buffer", "contentType", "maxAge", "upstreamEtag", "etag", "imageOptimizer", "value", "kind", "CachedRouteKind", "IMAGE", "extension", "cacheControl", "revalidate", "expire", "undefined", "routeKind", "RouteKind", "incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "originalResponse", "href", "isStatic", "isMiss", "isStale", "Boolean", "err", "message", "handleCatchallRenderRequest", "addRequestMeta", "routerServerGlobal", "RouterServerContextSymbol", "relativeProjectDir", "relative", "cwd", "dir", "existingServerContext", "bind", "removeTrailingSlash", "i18n", "i18nProvider", "fromRequest", "match", "render", "edgeFunctionsPages", "getEdgeFunctionsPages", "edgeFunctionsPage", "definition", "page", "NEXT_RSC_UNION_QUERY", "handled", "runEdgeFunction", "params", "appPaths", "apiError", "instrumentationOnRequestError", "routePath", "routerKind", "routeType", "revalidateReason", "isPagesAPIRouteMatch", "handleApiRequest", "NoFallbackError", "formatServerError", "logErrorWithOriginalStack", "logError", "renderError", "handleCatchallMiddlewareRequest", "parsed", "isMiddlewareInvoke", "handleFinished", "middleware", "getMiddleware", "initUrl", "parseUrl", "pathnameInfo", "getNextPathnameInfo", "normalizedPathname", "maybeDecodedPathname", "decodeURIComponent", "result", "bubblingResult", "ensureMiddleware", "url", "runMiddleware", "request", "response", "BubbledError", "key", "Object", "entries", "toNodeOutgoingHttpHeaders", "headers", "<PERSON><PERSON><PERSON><PERSON>", "status", "pipeToNodeResponse", "end", "isError", "code", "DecodeError", "getProperError", "finished", "isDev", "sriEnabled", "conf", "experimental", "sri", "algorithm", "optimizeCss", "__NEXT_OPTIMIZE_CSS", "nextScriptWorkers", "__NEXT_SCRIPT_WORKERS", "NEXT_DEPLOYMENT_ID", "useSkewCookie", "deploymentId", "ResponseCache", "appDocumentPreloading", "isDefaultEnabled", "loadComponents", "isAppPath", "catch", "preloadEntriesOnStart", "unstable_preloadEntries", "dynamicRoutes", "getRoutesManifest", "map", "r", "regex", "getRouteRegex", "getRouteMatcher", "re", "setHttpClientAndAgentOptions", "serverOptions", "experimentalTestProxy", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "middlewareManifestPath", "join", "serverDistDir", "MIDDLEWARE_MANIFEST", "prepare", "isExperimentalCompile", "populateStaticEnv", "removeUncaughtErrorAndRejectionListeners", "appPathsManifest", "getAppPathsManifest", "pagesManifest", "getPagesManifest", "loadCustomCacheHandlers", "keys", "ComponentMod", "patchFetch", "webpackRequire", "__next_app__", "m", "handleUpgrade", "loadInstrumentationModule", "instrumentation", "getInstrumentationModule", "cause", "prepareImpl", "runInstrumentationHookIfAvailable", "ensureInstrumentationRegistered", "loadEnvConfig", "forceReload", "silent", "Log", "cacheHandlers", "initializeCacheHandlers", "handler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interopDefault", "formatDynamicImportPath", "getIncrementalCache", "requestHeaders", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cache<PERSON><PERSON><PERSON>", "IncrementalCache", "fs", "getCacheFilesystem", "allowedRevalidateHeaderKeys", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "cacheMaxMemorySize", "flushToDisk", "isrFlushToDisk", "getPrerenderManifest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getResponseCache", "getPublicDir", "CLIENT_PUBLIC_FILES_PATH", "getHasStaticDir", "existsSync", "loadManifest", "PAGES_MANIFEST", "enabledDirectories", "app", "APP_PATHS_MANIFEST", "getinterceptionRoutePatterns", "routesManifest", "rewrites", "beforeFiles", "filter", "isInterceptionRouteRewrite", "rewrite", "RegExp", "hasPage", "getMaybePagePath", "locales", "getBuildId", "buildIdFile", "BUILD_ID_FILE", "readFileSync", "trim", "getEnabledDirectories", "findDir", "pages", "sendRenderResult", "type", "generateEtags", "poweredByHeader", "run<PERSON><PERSON>", "handledAsEdgeFunction", "parsedInitUrl", "search", "NodeModuleLoader", "module", "load", "filename", "waitUntil", "getWaitUntil", "renderHTML", "getTracer", "trace", "NextNodeServerSpan", "renderHTMLImpl", "nextFontManifest", "lazyRenderAppPage", "getServerComponentsHmrCache", "buildId", "lazyRenderPagesPage", "customServer", "isDraftMode", "developmentNotFoundSourcePage", "fetchExternalImage", "fetchInternalImage", "handleInternalReq", "newReq", "newRes", "routerServerHandler", "isAbsolute", "imageUpstream", "getPagePath", "renderPageComponent", "ctx", "bubbleNoFallback", "length", "getOriginalAppPaths", "findPageComponents", "locale", "spanName", "attributes", "normalizeAppPath", "findPageComponentsImpl", "_url", "pagePaths", "amp", "unshift", "normalizePagePath", "path", "pagePath", "components", "Component", "getStaticProps", "PageNotFoundError", "getNextFontManifest", "NEXT_FONT_MANIFEST", "_err", "_type", "ensurePage", "_opts", "nodeFs", "normalizeReq", "NodeNextRequest", "normalizeRes", "NodeNextResponse", "getRequestHandler", "makeRequestHandler", "wrapRequestHandlerNode", "url<PERSON><PERSON>", "revalidateHeaders", "opts", "mocked", "createRequestResponseMocks", "hasStreamed", "<PERSON><PERSON><PERSON><PERSON>", "unstable_onlyGenerated", "internal", "renderToHTML", "renderErrorToResponseImpl", "is404", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "clientOnly", "includes", "setHeaders", "renderErrorToHTML", "getMiddlewareManifest", "manifest", "middlewareModule", "loadNodeMiddleware", "config", "regexp", "originalSource", "functions", "getEdgeFunctionInfo", "foundPage", "denormalizePagePath", "pageInfo", "name", "paths", "files", "file", "wasm", "binding", "filePath", "assets", "nodeMiddleware", "functionsConfig", "FUNCTIONS_CONFIG_MANIFEST", "hasMiddleware", "ensureEdgeFunction", "_params", "checkIsOnDemandRevalidate", "previewProps", "isOnDemandRevalidate", "Response", "skipMiddlewareUrlNormalize", "urlQueryToSearchParams", "toString", "fetchHostname", "port", "middlewareInfo", "method", "toUpperCase", "requestData", "basePath", "trailingSlash", "signal", "signalFromNodeResponse", "MiddlewareNotFoundError", "adapterFn", "hasRequestBody", "cloneBodyStream", "finalize", "run", "edgeFunctionEntry", "useCache", "onWarning", "has", "cookies", "getSetCookie", "flatMap", "maybeCompoundCookie", "splitCookiesString", "delete", "cookie", "append", "_cachedPreviewManifest", "PRERENDER_MANIFEST", "ROUTES_MANIFEST", "attachRequestMeta", "isUpgradeReq", "protocol", "trustHostHeader", "host", "getCloneableBody", "edgeInfo", "isNextDataRequest", "initialUrl", "URL", "queryString", "fromEntries", "searchParams", "onError", "globalThis", "__incrementalCache", "serverComponentsHmrCache", "fetchMetrics", "statusMessage", "statusText", "for<PERSON>ach", "toLowerCase", "append<PERSON><PERSON>er", "_serverDistDir", "SERVER_DIRECTORY", "getFallbackErrorComponents", "args", "onServerClose", "listener", "add", "close", "runAll", "getInternalWaitUntil", "internalWaitUntil", "createInternalWaitUntil", "InvariantError", "awaiter", "Awaiter<PERSON>nce", "awaiting"], "mappings": ";;;;+BAqPA;;;eAAqBA;;;;QArPd;QACA;QACA;uBAOA;2DAiBQ;sBACgB;8BACC;6BACe;2BAaxC;8BACiB;sBAC0B;6BACjB;0BACR;6DACJ;gFAaE;yBACuB;qCACV;mCACF;gCACH;iEAES;wBACsB;wCACpB;qBACZ;6BACS;qCACH;qCACA;6BACH;0BACS;uEAInC;kCAC0B;0BACA;mCAEY;oCAER;wBAGG;4BACL;+BACZ;4BACO;8BACK;6BACQ;kCACN;6BACE;sCACV;8BACK;+BACE;gCACL;yCACS;oDAEG;2BAEjB;gCACK;yBACH;kCACK;0BACwB;2BAEvB;4BACP;kCACM;yCACD;gDAIzB;qCAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMP,yCAAyC;AACzC,MAAMC,0BAA0BC,QAAQC,GAAG,CAACC,YAAY,GACpD,CAACC,KACC,MAAM,CAAC,uBAAuB,GAAGA,IAAIC,IAAI,CAAC,CAACC,MAAQA,IAAIC,OAAO,IAAID,OACpE,CAACF,KAAe,MAAM,CAACA,IAAIC,IAAI,CAAC,CAACC,MAAQA,IAAIC,OAAO,IAAID;AAS5D,MAAME,yBAAyB,IAAIC;AAKnC,SAASC,qBACPC,IAA8C;IAE9C,MAAMC,SAASJ,uBAAuBK,GAAG,CAACF;IAC1C,IAAIC,QAAQ;QACV,OAAOA;IACT;IAEA,IAAI,CAACE,MAAMC,OAAO,CAACJ,KAAKK,QAAQ,GAAG;QACjC,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,2CAA2C,EAAEC,KAAKC,SAAS,CAACR,OAAO,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMS,UAAUC,IAAAA,iDAAyB,EAACV,KAAKK,QAAQ;IACvDR,uBAAuBc,GAAG,CAACX,MAAMS;IACjC,OAAOA;AACT;AAEA,SAASG,4BACPC,8CAAuD;IAEvD,oEAAoE;IACpE,kDAAkD;IAClD,EAAE;IACF,2EAA2E;IAC3E,2BAA2B;IAC3B,EAAE;IACF,qEAAqE;IACrE,yEAAyE;IACzE,oEAAoE;IACpE,gEAAgE;IAChE,oEAAoE;IACpE,kDAAkD;IAClD,EAAE;IACF,kCAAkC;IAClC,gDAAgD;IAChD,wBAAwB;IACxB,+CAA+C;IAC/C,QAAQ;IACR,EAAE;IACF,0EAA0E;IAC1E,qEAAqE;IACrE,yEAAyE;IACzE,iCAAiC;IACjC,EAAE;IACF,0EAA0E;IAC1E,4EAA4E;IAC5E,EAAE;IACF,yEAAyE;IACzE,4BAA4B;IAC5B,EAAE;IACF,oEAAoE;IACpE,+CAA+C;IAC/C,EAAE;IACF,uEAAuE;IACvE,wDAAwD;IAExD,6EAA6E;IAC7E,8EAA8E;IAC9E,4EAA4E;IAC5E,mBAAmB;IACnB,IAAIA,gDAAgD;QAClDvB,QAAQwB,kBAAkB,CAAC;QAC3BxB,QAAQwB,kBAAkB,CAAC;IAC7B;IAEA,8DAA8D;IAC9DxB,QAAQyB,EAAE,CAAC,sBAAsB,CAACC;QAChC,IAAIC,IAAAA,sBAAU,EAACD,SAAS;YACtB,0EAA0E;YAC1E,qDAAqD;YACrD;QACF;QACA,6BAA6B;QAC7B,yEAAyE;QACzE,yEAAyE;QACzE,wEAAwE;QACxE,wEAAwE;QACxE,yEAAyE;QACzE,oDAAoD;QACpDE,QAAQC,KAAK,CAACH;IAChB;IAEA1B,QAAQyB,EAAE,CAAC,oBAAoB;IAC7B,yEAAyE;IACzE,oEAAoE;IACpE,gBAAgB;IAClB;IAEA,4EAA4E;IAC5E,2EAA2E;IAC3E,qDAAqD;IACrDzB,QAAQyB,EAAE,CAAC,qBAAqB,CAACC;QAC/B,IAAIC,IAAAA,sBAAU,EAACD,SAAS;YACtB;QACF;QACAE,QAAQC,KAAK,CAACH;IAChB;AACF;AAEe,MAAM5B,uBAAuBgC,mBAAU;IAyBpDC,YAAYC,OAAgB,CAAE;YAMFA,gCAAAA,4BAqGxBA;QA1GF,yBAAyB;QACzB,KAAK,CAACA,eAnBAC,4BAAqC,YAYnCC,mBAAmB,IAAIC,kCAAgB,SA6rBvCC,yBAA2C,OACnDC,KACAC,KACAC;YAEA,IAAI,CAACA,UAAUC,QAAQ,IAAI,CAACD,UAAUC,QAAQ,CAACC,UAAU,CAAC,iBAAiB;gBACzE,OAAO;YACT;YACA,qCAAqC;YACrC,IAAIC,IAAAA,2BAAc,EAACL,KAAK,qBAAqB;gBAC3C,OAAO;YACT;YAEA,IACE,IAAI,CAACM,WAAW,IAChB,IAAI,CAACC,UAAU,CAACC,MAAM,KAAK,YAC3B7C,QAAQC,GAAG,CAACC,YAAY,EACxB;gBACAoC,IAAIQ,UAAU,GAAG;gBACjBR,IAAIS,IAAI,CAAC,eAAeC,IAAI;gBAC5B,OAAO;YACP,+CAA+C;YACjD,OAAO;gBACL,MAAM,EAAEC,mBAAmB,EAAE,GAC3BC,QAAQ;gBAEV,MAAMC,sBAAsB,IAAIF,oBAAoB;oBAClDG,SAAS,IAAI,CAACA,OAAO;oBACrBR,YAAY,IAAI,CAACA,UAAU;gBAC7B;gBAEA,MAAM,EAAES,YAAY,EAAEC,UAAU,EAAE,GAChCJ,QAAQ;gBAEV,IAAI,CAAC,IAAI,CAACK,kBAAkB,EAAE;oBAC5B,MAAM,qBAAgE,CAAhE,IAAIvC,MAAM,wDAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA+D;gBACvE;gBACA,MAAMwC,eAAe,IAAI,CAACZ,UAAU,CAACa,MAAM;gBAE3C,IAAID,aAAaE,MAAM,KAAK,aAAaF,aAAaG,WAAW,EAAE;oBACjE,MAAM,IAAI,CAACC,SAAS,CAACvB,KAAKC;oBAC1B,OAAO;gBACT;gBAEA,MAAMuB,eAAeZ,oBAAoBa,cAAc,CACrDzB,IAAI0B,eAAe,EACnBxB,UAAUyB,KAAK,EACf,IAAI,CAACpB,UAAU,EACf,CAAC,CAAC,IAAI,CAACqB,UAAU,CAACC,GAAG;gBAGvB,IAAI,kBAAkBL,cAAc;oBAClCvB,IAAIQ,UAAU,GAAG;oBACjBR,IAAIS,IAAI,CAACc,aAAaM,YAAY,EAAEnB,IAAI;oBACxC,OAAO;gBACT;gBAEA,MAAMoB,WAAWnB,oBAAoBoB,WAAW,CAACR;gBAEjD,IAAI;wBAgCES,mBAgBFA;oBA/CF,MAAM,EAAEC,YAAY,EAAE,GACpBrB,QAAQ;oBACV,MAAMoB,aAAa,MAAM,IAAI,CAACf,kBAAkB,CAAC3C,GAAG,CAClDwD,UACA,OAAO,EAAEI,kBAAkB,EAAE;wBAC3B,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,MAAM,EAAEC,YAAY,EAAEC,IAAI,EAAE,GACvD,MAAM,IAAI,CAACC,cAAc,CACvBzC,KACAC,KACAuB,cACAW;wBAGJ,OAAO;4BACLO,OAAO;gCACLC,MAAMC,8BAAe,CAACC,KAAK;gCAC3BT;gCACAI;gCACAM,WAAWZ,aAAaG;gCACxBE;4BACF;4BACAQ,cAAc;gCAAEC,YAAYV;gCAAQW,QAAQC;4BAAU;wBACxD;oBACF,GACA;wBACEC,WAAWC,oBAAS,CAACP,KAAK;wBAC1BQ,kBAAkBvC;wBAClBwC,YAAY;oBACd;oBAGF,IAAIrB,CAAAA,+BAAAA,oBAAAA,WAAYS,KAAK,qBAAjBT,kBAAmBU,IAAI,MAAKC,8BAAe,CAACC,KAAK,EAAE;wBACrD,MAAM,qBAEL,CAFK,IAAIlE,MACR,0DADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEAqC,aACEhB,IAAI0B,eAAe,EACnBzB,IAAIsD,gBAAgB,EACpB/B,aAAagC,IAAI,EACjBvB,WAAWS,KAAK,CAACI,SAAS,EAC1Bb,WAAWS,KAAK,CAACN,MAAM,EACvBH,WAAWS,KAAK,CAACF,IAAI,EACrBhB,aAAaiC,QAAQ,EACrBxB,WAAWyB,MAAM,GAAG,SAASzB,WAAW0B,OAAO,GAAG,UAAU,OAC5DxC,cACAc,EAAAA,2BAAAA,WAAWc,YAAY,qBAAvBd,yBAAyBe,UAAU,KAAI,GACvCY,QAAQ,IAAI,CAAChC,UAAU,CAACC,GAAG;oBAE7B,OAAO;gBACT,EAAE,OAAOgC,KAAK;oBACZ,IAAIA,eAAe5C,YAAY;wBAC7BhB,IAAIQ,UAAU,GAAGoD,IAAIpD,UAAU;wBAC/BR,IAAIS,IAAI,CAACmD,IAAIC,OAAO,EAAEnD,IAAI;wBAC1B,OAAO;oBACT;oBACA,MAAMkD;gBACR;YACF;QACF,QAEUE,8BAAgD,OACxD/D,KACAC,KACAC;YAEA,IAAI,EAAEC,QAAQ,EAAEwB,KAAK,EAAE,GAAGzB;YAC1B,IAAI,CAACC,UAAU;gBACb,MAAM,qBAA6C,CAA7C,IAAIxB,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;YAEA,wEAAwE;YACxE,QAAQ;YACRqF,IAAAA,2BAAc,EAAChE,KAAK,oBAAoB;YAExC,0DAA0D;YAC1D,sDAAsD;YACtD,IAAI,CAACiE,uCAAkB,CAACC,8CAAyB,CAAC,EAAE;gBAClDD,uCAAkB,CAACC,8CAAyB,CAAC,GAAG,CAAC;YACnD;YACA,MAAMC,qBAAqBC,IAAAA,cAAQ,EAACzG,QAAQ0G,GAAG,IAAI,IAAI,CAACC,GAAG;YAC3D,MAAMC,wBACJN,uCAAkB,CAACC,8CAAyB,CAAC,CAACC,mBAAmB;YAEnE,IAAI,CAACI,uBAAuB;gBAC1BN,uCAAkB,CAACC,8CAAyB,CAAC,CAACC,mBAAmB,GAAG;oBAClE5C,WAAW,IAAI,CAACA,SAAS,CAACiD,IAAI,CAAC,IAAI;gBACrC;YACF;YACAP,uCAAkB,CAACC,8CAAyB,CAAC,CAC3CC,mBACD,CAAC5D,UAAU,GAAG,IAAI,CAACA,UAAU;YAE9B,IAAI;oBAKM;gBAJR,wDAAwD;gBACxDJ,WAAWsE,IAAAA,wCAAmB,EAACtE;gBAE/B,MAAMR,UAAwB;oBAC5B+E,IAAI,GAAE,qBAAA,IAAI,CAACC,YAAY,qBAAjB,mBAAmBC,WAAW,CAAC5E,KAAKG;gBAC5C;gBACA,MAAM0E,QAAQ,MAAM,IAAI,CAACnG,QAAQ,CAACmG,KAAK,CAAC1E,UAAUR;gBAElD,sDAAsD;gBACtD,IAAI,CAACkF,OAAO;oBACV,MAAM,IAAI,CAACC,MAAM,CAAC9E,KAAKC,KAAKE,UAAUwB,OAAOzB,WAAW;oBAExD,OAAO;gBACT;gBAEA,sEAAsE;gBACtE,wBAAwB;gBACxB8D,IAAAA,2BAAc,EAAChE,KAAK,SAAS6E;gBAE7B,yCAAyC;gBACzC,MAAME,qBAAqB,IAAI,CAACC,qBAAqB;gBACrD,KAAK,MAAMC,qBAAqBF,mBAAoB;oBAClD,6DAA6D;oBAC7D,IAAIE,sBAAsBJ,MAAMK,UAAU,CAACC,IAAI,EAAE;oBAEjD,IAAI,IAAI,CAAC5E,UAAU,CAACC,MAAM,KAAK,UAAU;wBACvC,MAAM,IAAI,CAACe,SAAS,CAACvB,KAAKC,KAAKC;wBAC/B,OAAO;oBACT;oBACA,OAAOyB,KAAK,CAACyD,sCAAoB,CAAC;oBAElC,kDAAkD;oBAClD,8BAA8B;oBAC9B,IAAI;wBACF,MAAMC,UAAU,MAAM,IAAI,CAACC,eAAe,CAAC;4BACzCtF;4BACAC;4BACA0B;4BACA4D,QAAQV,MAAMU,MAAM;4BACpBJ,MAAMN,MAAMK,UAAU,CAACC,IAAI;4BAC3BN;4BACAW,UAAU;wBACZ;wBACA,IAAIH,SAAS,OAAO;oBACtB,EAAE,OAAOI,UAAU;wBACjB,MAAM,IAAI,CAACC,6BAA6B,CAACD,UAAUzF,KAAK;4BACtD2F,WAAWd,MAAMK,UAAU,CAACC,IAAI;4BAChCS,YAAY;4BACZC,WAAW;4BACX,oCAAoC;4BACpCC,kBAAkB5C;wBACpB;wBACA,MAAMuC;oBACR;gBACF;gBAEA,oEAAoE;gBACpE,MAAM;gBACN,iDAAiD;gBACjD,IAAIM,IAAAA,wCAAoB,EAAClB,QAAQ;oBAC/B,IAAI,IAAI,CAACtE,UAAU,CAACC,MAAM,KAAK,UAAU;wBACvC,MAAM,IAAI,CAACe,SAAS,CAACvB,KAAKC,KAAKC;wBAC/B,OAAO;oBACT;oBAEA,MAAMmF,UAAU,MAAM,IAAI,CAACW,gBAAgB,CAAChG,KAAKC,KAAK0B,OAAOkD;oBAC7D,IAAIQ,SAAS,OAAO;gBACtB;gBAEA,MAAM,IAAI,CAACP,MAAM,CAAC9E,KAAKC,KAAKE,UAAUwB,OAAOzB,WAAW;gBAExD,OAAO;YACT,EAAE,OAAO2D,KAAU;gBACjB,IAAIA,eAAeoC,wCAAe,EAAE;oBAClC,MAAMpC;gBACR;gBAEA,IAAI;oBACF,IAAI,IAAI,CAACjC,UAAU,CAACC,GAAG,EAAE;wBACvB,MAAM,EAAEqE,iBAAiB,EAAE,GACzBrF,QAAQ;wBACVqF,kBAAkBrC;wBAClB,IAAI,CAACsC,yBAAyB,CAACtC;oBACjC,OAAO;wBACL,IAAI,CAACuC,QAAQ,CAACvC;oBAChB;oBACA5D,IAAIQ,UAAU,GAAG;oBACjB,MAAM,IAAI,CAAC4F,WAAW,CAACxC,KAAK7D,KAAKC,KAAKE,UAAUwB;oBAChD,OAAO;gBACT,EAAE,OAAM,CAAC;gBAET,MAAMkC;YACR;QACF,QAmjBUyC,kCAAoD,OAC5DtG,KACAC,KACAsG;YAEA,MAAMC,qBAAqBnG,IAAAA,2BAAc,EAACL,KAAK;YAE/C,IAAI,CAACwG,oBAAoB;gBACvB,OAAO;YACT;YAEA,MAAMC,iBAAiB;gBACrBzC,IAAAA,2BAAc,EAAChE,KAAK,oBAAoB;gBACxCC,IAAIS,IAAI,CAAC,IAAIC,IAAI;gBACjB,OAAO;YACT;YAEA,MAAM+F,aAAa,MAAM,IAAI,CAACC,aAAa;YAC3C,IAAI,CAACD,YAAY;gBACf,OAAOD;YACT;YAEA,MAAMG,UAAUvG,IAAAA,2BAAc,EAACL,KAAK;YACpC,MAAME,YAAY2G,IAAAA,kBAAQ,EAACD;YAC3B,MAAME,eAAeC,IAAAA,wCAAmB,EAAC7G,UAAUC,QAAQ,EAAE;gBAC3DI,YAAY,IAAI,CAACA,UAAU;gBAC3BoE,cAAc,IAAI,CAACA,YAAY;YACjC;YAEAzE,UAAUC,QAAQ,GAAG2G,aAAa3G,QAAQ;YAC1C,MAAM6G,qBAAqBvC,IAAAA,wCAAmB,EAAC8B,OAAOpG,QAAQ,IAAI;YAClE,IAAI8G,uBAAuBD;YAE3B,IAAI;gBACFC,uBAAuBC,mBAAmBF;YAC5C,EAAE,OAAM;YACN,+CAA+C,GACjD;YAEA,IACE,CACEN,CAAAA,WAAW7B,KAAK,CAACmC,oBAAoBhH,KAAKE,UAAUyB,KAAK,KACzD+E,WAAW7B,KAAK,CAACoC,sBAAsBjH,KAAKE,UAAUyB,KAAK,CAAA,GAE7D;gBACA,OAAO8E;YACT;YAEA,IAAIU;YAGJ,IAAIC,iBAAiB;YAErB,IAAI;gBACF,MAAM,IAAI,CAACC,gBAAgB,CAACrH,IAAIsH,GAAG;gBAEnCH,SAAS,MAAM,IAAI,CAACI,aAAa,CAAC;oBAChCC,SAASxH;oBACTyH,UAAUxH;oBACVC,WAAWA;oBACXqG,QAAQA;gBACV;gBAEA,IAAI,cAAcY,QAAQ;oBACxB,IAAIX,oBAAoB;wBACtBY,iBAAiB;wBACjB,MAAM,qBAA8B,CAA9B,IAAIM,oBAAY,CAAC,MAAMP,SAAvB,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6B;oBACrC;oBAEA,KAAK,MAAM,CAACQ,KAAKjF,MAAM,IAAIkF,OAAOC,OAAO,CACvCC,IAAAA,iCAAyB,EAACX,OAAOM,QAAQ,CAACM,OAAO,GAChD;wBACD,IAAIJ,QAAQ,sBAAsBjF,UAAUQ,WAAW;4BACrDjD,IAAI+H,SAAS,CAACL,KAAKjF;wBACrB;oBACF;oBACAzC,IAAIQ,UAAU,GAAG0G,OAAOM,QAAQ,CAACQ,MAAM;oBAEvC,MAAM,EAAE1E,gBAAgB,EAAE,GAAGtD;oBAC7B,IAAIkH,OAAOM,QAAQ,CAAC/G,IAAI,EAAE;wBACxB,MAAMwH,IAAAA,gCAAkB,EAACf,OAAOM,QAAQ,CAAC/G,IAAI,EAAE6C;oBACjD,OAAO;wBACLA,iBAAiB4E,GAAG;oBACtB;oBACA,OAAO;gBACT;YACF,EAAE,OAAOtE,KAAc;gBACrB,IAAIuD,gBAAgB;oBAClB,MAAMvD;gBACR;gBAEA,IAAIuE,IAAAA,gBAAO,EAACvE,QAAQA,IAAIwE,IAAI,KAAK,UAAU;oBACzC,MAAM,IAAI,CAAC9G,SAAS,CAACvB,KAAKC,KAAKsG;oBAC/B,OAAO;gBACT;gBAEA,IAAI1C,eAAeyE,kBAAW,EAAE;oBAC9BrI,IAAIQ,UAAU,GAAG;oBACjB,MAAM,IAAI,CAAC4F,WAAW,CAACxC,KAAK7D,KAAKC,KAAKsG,OAAOpG,QAAQ,IAAI;oBACzD,OAAO;gBACT;gBAEA,MAAMX,QAAQ+I,IAAAA,uBAAc,EAAC1E;gBAC7BtE,QAAQC,KAAK,CAACA;gBACdS,IAAIQ,UAAU,GAAG;gBACjB,MAAM,IAAI,CAAC4F,WAAW,CAAC7G,OAAOQ,KAAKC,KAAKsG,OAAOpG,QAAQ,IAAI;gBAC3D,OAAO;YACT;YAEA,OAAOgH,OAAOqB,QAAQ;QACxB;QA7kDE,MAAMC,QAAQ9I,QAAQkC,GAAG,IAAI;QAC7B,IAAI,CAAC4G,KAAK,GAAGA;QACb,IAAI,CAACC,UAAU,GAAG9E,SAAQjE,6BAAAA,QAAQgJ,IAAI,CAACC,YAAY,sBAAzBjJ,iCAAAA,2BAA2BkJ,GAAG,qBAA9BlJ,+BAAgCmJ,SAAS;QAEnE;;;;KAIC,GACD,IAAI,IAAI,CAAClH,UAAU,CAACmH,WAAW,EAAE;YAC/BpL,QAAQC,GAAG,CAACoL,mBAAmB,GAAGpK,KAAKC,SAAS,CAAC;QACnD;QACA,IAAI,IAAI,CAAC+C,UAAU,CAACqH,iBAAiB,EAAE;YACrCtL,QAAQC,GAAG,CAACsL,qBAAqB,GAAGtK,KAAKC,SAAS,CAAC;QACrD;QACAlB,QAAQC,GAAG,CAACuL,kBAAkB,GAAG,IAAI,CAAC5I,UAAU,CAACqI,YAAY,CAACQ,aAAa,GACvE,KACA,IAAI,CAAC7I,UAAU,CAAC8I,YAAY,IAAI;QAEpC,IAAI,CAAC,IAAI,CAAC/I,WAAW,EAAE;YACrB,IAAI,CAACY,kBAAkB,GAAG,IAAIoI,sBAAa,CAAC,IAAI,CAAChJ,WAAW;QAC9D;QAEA,MAAM,EAAEiJ,qBAAqB,EAAE,GAAG,IAAI,CAAChJ,UAAU,CAACqI,YAAY;QAC9D,MAAMY,mBAAmB,OAAOD,0BAA0B;QAE1D,IACE,CAAC5J,QAAQkC,GAAG,IACX0H,CAAAA,0BAA0B,QACzB,CAAE,CAAA,IAAI,CAACjJ,WAAW,IAAIkJ,gBAAe,CAAC,GACxC;YACA,+CAA+C;YAC/C,2BAA2B;YAC3BC,IAAAA,8BAAc,EAAC;gBACb1I,SAAS,IAAI,CAACA,OAAO;gBACrBoE,MAAM;gBACNuE,WAAW;gBACXjB,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GAAGiB,KAAK,CAAC,KAAO;YAChBF,IAAAA,8BAAc,EAAC;gBACb1I,SAAS,IAAI,CAACA,OAAO;gBACrBoE,MAAM;gBACNuE,WAAW;gBACXjB,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GAAGiB,KAAK,CAAC,KAAO;QAClB;QAEA,IACE,CAAChK,QAAQkC,GAAG,IACZ,CAAC,IAAI,CAACvB,WAAW,IACjB,IAAI,CAACC,UAAU,CAACqI,YAAY,CAACgB,qBAAqB,EAClD;YACA,IAAI,CAACC,uBAAuB;QAC9B;QAEA,IAAI,CAAClK,QAAQkC,GAAG,EAAE;YAChB,MAAM,EAAEiI,gBAAgB,EAAE,EAAE,GAAG,IAAI,CAACC,iBAAiB,MAAM,CAAC;YAC5D,IAAI,CAACD,aAAa,GAAGA,cAAcE,GAAG,CAAC,CAACC;gBACtC,wDAAwD;gBACxD,MAAMC,QAAQC,IAAAA,yBAAa,EAACF,EAAE9E,IAAI;gBAClC,MAAMN,QAAQuF,IAAAA,6BAAe,EAACF;gBAE9B,OAAO;oBACLrF;oBACAM,MAAM8E,EAAE9E,IAAI;oBACZkF,IAAIH,MAAMG,EAAE;gBACd;YACF;QACF;QAEA,sDAAsD;QACtDC,IAAAA,+CAA4B,EAAC,IAAI,CAAC/J,UAAU;QAE5C,2CAA2C;QAC3C,IAAI,IAAI,CAACgK,aAAa,CAACC,qBAAqB,EAAE;YAC5C7M,QAAQC,GAAG,CAAC6M,uBAAuB,GAAG;YACtC,MAAM,EAAEC,iBAAiB,EAAE,GACzB,sHAAsH;YACtH7J,QAAQ;YACV6J;QACF;QAEA,IAAI,CAACC,sBAAsB,GAAGC,IAAAA,UAAI,EAAC,IAAI,CAACC,aAAa,EAAEC,8BAAmB;QAE1E,4EAA4E;QAC5E,2EAA2E;QAC3E,qEAAqE;QACrE,uEAAuE;QACvE,IAAI,CAACnL,QAAQkC,GAAG,EAAE;YAChB,IAAI,CAACkJ,OAAO,GAAGpB,KAAK,CAAC,CAAC9F;gBACpBtE,QAAQC,KAAK,CAAC,4BAA4BqE;YAC5C;QACF;QAEA,yDAAyD;QACzD,yCAAyC;QACzC,IAAI,IAAI,CAACjC,UAAU,CAACoJ,qBAAqB,EAAE;YACzCC,IAAAA,4BAAiB,EAAC,IAAI,CAAC1K,UAAU;QACnC;QAEA,MAAMrB,iDAAiD0E,SACrDjE,8BAAAA,QAAQgJ,IAAI,CAACC,YAAY,qBAAzBjJ,4BAA2BuL,wCAAwC;QAErEjM,4BAA4BC;IAC9B;IAEA,MAAa2K,0BAAyC;QACpD,qEAAqE;QACrE,MAAM,IAAI,CAACkB,OAAO;QAElB,MAAMI,mBAAmB,IAAI,CAACC,mBAAmB;QACjD,MAAMC,gBAAgB,IAAI,CAACC,gBAAgB;QAE3C,MAAM,IAAI,CAACC,uBAAuB;QAElC,KAAK,MAAMpG,QAAQyC,OAAO4D,IAAI,CAACH,iBAAiB,CAAC,GAAI;YACnD,MAAM5B,IAAAA,8BAAc,EAAC;gBACnB1I,SAAS,IAAI,CAACA,OAAO;gBACrBoE;gBACAuE,WAAW;gBACXjB,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GAAGiB,KAAK,CAAC,KAAO;QAClB;QAEA,KAAK,MAAMxE,QAAQyC,OAAO4D,IAAI,CAACL,oBAAoB,CAAC,GAAI;YACtD,MAAM1B,IAAAA,8BAAc,EAAC;gBACnB1I,SAAS,IAAI,CAACA,OAAO;gBACrBoE;gBACAuE,WAAW;gBACXjB,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GACG3K,IAAI,CAAC,OAAO,EAAE0N,YAAY,EAAE;gBAC3B,iEAAiE;gBACjE,yEAAyE;gBACzE,oDAAoD;gBACpDA,aAAaC,UAAU;gBAEvB,MAAMC,iBAAiBF,aAAaG,YAAY,CAAC/K,OAAO;gBACxD,IAAI8K,kCAAAA,eAAgBE,CAAC,EAAE;oBACrB,KAAK,MAAM/N,MAAM8J,OAAO4D,IAAI,CAACG,eAAeE,CAAC,EAAG;wBAC9C,MAAMF,eAAe7N;oBACvB;gBACF;YACF,GACC6L,KAAK,CAAC,KAAO;QAClB;IACF;IAEA,MAAgBmC,gBAA+B;IAC7C,yEAAyE;IACzE,eAAe;IACjB;IAEA,MAAgBC,4BAA4B;QAC1C,IAAI,CAAC,IAAI,CAACxB,aAAa,CAAC1I,GAAG,EAAE;YAC3B,IAAI;gBACF,IAAI,CAACmK,eAAe,GAAG,MAAMC,IAAAA,wDAAwB,EACnD,IAAI,CAAC3H,GAAG,EACR,IAAI,CAAC/D,UAAU,CAACQ,OAAO;YAE3B,EAAE,OAAO8C,KAAU;gBACjB,IAAIA,IAAIwE,IAAI,KAAK,oBAAoB;oBACnC,MAAM,qBAGL,CAHK,IAAI1J,MACR,4DACA;wBAAEuN,OAAOrI;oBAAI,IAFT,qBAAA;+BAAA;oCAAA;sCAAA;oBAGN;gBACF;YACF;QACF;QACA,OAAO,IAAI,CAACmI,eAAe;IAC7B;IAEA,MAAgBG,cAAc;QAC5B,MAAM,KAAK,CAACA;QACZ,MAAM,IAAI,CAACC,iCAAiC;IAC9C;IAEA,MAAgBA,oCAAoC;QAClD,MAAMC,IAAAA,+DAA+B,EAAC,IAAI,CAAC/H,GAAG,EAAE,IAAI,CAAC/D,UAAU,CAACQ,OAAO;IACzE;IAEUuL,cAAc,EACtBzK,GAAG,EACH0K,WAAW,EACXC,MAAM,EAKP,EAAE;QACDF,IAAAA,kBAAa,EACX,IAAI,CAAChI,GAAG,EACRzC,KACA2K,SAAS;YAAEnO,MAAM,KAAO;YAAGmB,OAAO,KAAO;QAAE,IAAIiN,MAC/CF;IAEJ;IAEA,MAAchB,0BAA0B;QACtC,MAAM,EAAEmB,aAAa,EAAE,GAAG,IAAI,CAACnM,UAAU,CAACqI,YAAY;QACtD,IAAI,CAAC8D,eAAe;QAEpB,yEAAyE;QACzE,SAAS;QACT,IAAI,CAACC,IAAAA,iCAAuB,KAAI;QAEhC,KAAK,MAAM,CAAChK,MAAMiK,QAAQ,IAAIhF,OAAOC,OAAO,CAAC6E,eAAgB;YAC3D,IAAI,CAACE,SAAS;YAEdC,IAAAA,yBAAe,EACblK,MACAmK,IAAAA,8BAAc,EACZ,MAAMpP,wBACJqP,IAAAA,gDAAuB,EAAC,IAAI,CAAChM,OAAO,EAAE6L;QAI9C;IACF;IAEA,MAAgBI,oBAAoB,EAClCC,cAAc,EAGf,EAAE;QACD,MAAMpL,MAAM,CAAC,CAAC,IAAI,CAACD,UAAU,CAACC,GAAG;QACjC,IAAIqL;QACJ,MAAM,EAAEC,YAAY,EAAE,GAAG,IAAI,CAAC5M,UAAU;QAExC,IAAI4M,cAAc;YAChBD,eAAeJ,IAAAA,8BAAc,EAC3B,MAAMpP,wBACJqP,IAAAA,gDAAuB,EAAC,IAAI,CAAChM,OAAO,EAAEoM;QAG5C;QAEA,MAAM,IAAI,CAAC5B,uBAAuB;QAElC,wCAAwC;QACxC,kDAAkD;QAClD,oBAAoB;QACpB,OAAO,IAAI6B,kCAAgB,CAAC;YAC1BC,IAAI,IAAI,CAACC,kBAAkB;YAC3BzL;YACAoL;YACAM,6BACE,IAAI,CAAChN,UAAU,CAACqI,YAAY,CAAC2E,2BAA2B;YAC1DjN,aAAa,IAAI,CAACA,WAAW;YAC7BuK,eAAe,IAAI,CAACA,aAAa;YACjC2C,qBAAqB,IAAI,CAACjN,UAAU,CAACqI,YAAY,CAAC4E,mBAAmB;YACrEC,oBAAoB,IAAI,CAAClN,UAAU,CAACmN,kBAAkB;YACtDC,aACE,CAAC,IAAI,CAACrN,WAAW,IAAI,IAAI,CAACC,UAAU,CAACqI,YAAY,CAACgF,cAAc;YAClEC,sBAAsB,IAAM,IAAI,CAACA,oBAAoB;YACrDC,iBAAiBZ;QACnB;IACF;IAEUa,mBAAmB;QAC3B,OAAO,IAAIzE,sBAAa,CAAC,IAAI,CAAChJ,WAAW;IAC3C;IAEU0N,eAAuB;QAC/B,OAAOpD,IAAAA,UAAI,EAAC,IAAI,CAACtG,GAAG,EAAE2J,mCAAwB;IAChD;IAEUC,kBAA2B;QACnC,OAAOb,WAAE,CAACc,UAAU,CAACvD,IAAAA,UAAI,EAAC,IAAI,CAACtG,GAAG,EAAE;IACtC;IAEUgH,mBAA8C;QACtD,OAAO8C,IAAAA,kCAAY,EACjBxD,IAAAA,UAAI,EAAC,IAAI,CAACC,aAAa,EAAEwD,yBAAc;IAE3C;IAEUjD,sBAAiD;QACzD,IAAI,CAAC,IAAI,CAACkD,kBAAkB,CAACC,GAAG,EAAE,OAAOrL;QAEzC,OAAOkL,IAAAA,kCAAY,EACjBxD,IAAAA,UAAI,EAAC,IAAI,CAACC,aAAa,EAAE2D,6BAAkB;IAE/C;IAEUC,+BAAyC;QACjD,IAAI,CAAC,IAAI,CAACH,kBAAkB,CAACC,GAAG,EAAE,OAAO,EAAE;QAE3C,MAAMG,iBAAiB,IAAI,CAAC3E,iBAAiB;QAC7C,OACE2E,CAAAA,kCAAAA,eAAgBC,QAAQ,CAACC,WAAW,CACjCC,MAAM,CAACC,8DAA0B,EACjC9E,GAAG,CAAC,CAAC+E,UAAY,IAAIC,OAAOD,QAAQ7E,KAAK,OAAM,EAAE;IAExD;IAEA,MAAgB+E,QAAQ9O,QAAgB,EAAoB;YAIxD;QAHF,OAAO,CAAC,CAAC+O,IAAAA,yBAAgB,EACvB/O,UACA,IAAI,CAACY,OAAO,GACZ,wBAAA,IAAI,CAACR,UAAU,CAACmE,IAAI,qBAApB,sBAAsByK,OAAO,EAC7B,IAAI,CAACb,kBAAkB,CAACC,GAAG;IAE/B;IAEUa,aAAqB;QAC7B,MAAMC,cAAczE,IAAAA,UAAI,EAAC,IAAI,CAAC7J,OAAO,EAAEuO,wBAAa;QACpD,IAAI;YACF,OAAOjC,WAAE,CAACkC,YAAY,CAACF,aAAa,QAAQG,IAAI;QAClD,EAAE,OAAO3L,KAAU;YACjB,IAAIA,IAAIwE,IAAI,KAAK,UAAU;gBACzB,MAAM,qBAEL,CAFK,IAAI1J,MACR,CAAC,0CAA0C,EAAE,IAAI,CAACoC,OAAO,CAAC,yJAAyJ,CAAC,GADhN,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,MAAM8C;QACR;IACF;IAEU4L,sBAAsB5N,GAAY,EAA0B;QACpE,MAAMyC,MAAMzC,MAAM,IAAI,CAACyC,GAAG,GAAG,IAAI,CAACuG,aAAa;QAE/C,OAAO;YACL0D,KAAKmB,IAAAA,qBAAO,EAACpL,KAAK,SAAS,OAAO;YAClCqL,OAAOD,IAAAA,qBAAO,EAACpL,KAAK,WAAW,OAAO;QACxC;IACF;IAEUsL,iBACR5P,GAAoB,EACpBC,GAAqB,EACrBN,OAMC,EACc;QACf,OAAOiQ,IAAAA,6BAAgB,EAAC;YACtB5P,KAAKA,IAAI0B,eAAe;YACxBzB,KAAKA,IAAIsD,gBAAgB;YACzB4D,QAAQxH,QAAQwH,MAAM;YACtB0I,MAAMlQ,QAAQkQ,IAAI;YAClBC,eAAenQ,QAAQmQ,aAAa;YACpCC,iBAAiBpQ,QAAQoQ,eAAe;YACxChN,cAAcpD,QAAQoD,YAAY;QACpC;IACF;IAEA,MAAgBiN,OACdhQ,GAAoB,EACpBC,GAAqB,EACrB0B,KAAqB,EACrBkD,KAAyB,EACP;QAClB,MAAME,qBAAqB,IAAI,CAACC,qBAAqB;QAErD,KAAK,MAAMC,qBAAqBF,mBAAoB;YAClD,IAAIE,sBAAsBJ,MAAMK,UAAU,CAAC/E,QAAQ,EAAE;gBACnD,MAAM8P,wBAAwB,MAAM,IAAI,CAAC3K,eAAe,CAAC;oBACvDtF;oBACAC;oBACA0B;oBACA4D,QAAQV,MAAMU,MAAM;oBACpBJ,MAAMN,MAAMK,UAAU,CAAC/E,QAAQ;oBAC/BqF,UAAU;gBACZ;gBAEA,IAAIyK,uBAAuB;oBACzB,OAAO;gBACT;YACF;QACF;QACA,6DAA6D;QAC7D,+DAA+D;QAC/D,MAAMC,gBAAgBrJ,IAAAA,kBAAQ,EAACxG,IAAAA,2BAAc,EAACL,KAAK,cAAcA,IAAIsH,GAAG;QACxEtH,IAAIsH,GAAG,GAAG,GAAG4I,cAAc/P,QAAQ,GAAG+P,cAAcC,MAAM,IAAI,IAAI;QAElE,MAAM9O,SAAS,IAAI+O,kCAAgB;QACnC,MAAMC,SAAU,MAAMhP,OAAOiP,IAAI,CAACzL,MAAMK,UAAU,CAACqL,QAAQ;QAS3DvM,IAAAA,2BAAc,EAAChE,IAAI0B,eAAe,EAAE,cAAc,IAAI,CAAC4C,GAAG;QAC1DN,IAAAA,2BAAc,EAAChE,IAAI0B,eAAe,EAAE,WAAW,IAAI,CAACX,OAAO;QAC3D,MAAMsP,OAAOzD,OAAO,CAAC5M,IAAI0B,eAAe,EAAEzB,IAAIsD,gBAAgB,EAAE;YAC9DiN,WAAW,IAAI,CAACC,YAAY;QAC9B;QACA,OAAO;IACT;IAEA,MAAgBC,WACd1Q,GAAoB,EACpBC,GAAqB,EACrBE,QAAgB,EAChBwB,KAAyB,EACzBC,UAA4B,EACL;QACvB,OAAO+O,IAAAA,iBAAS,IAAGC,KAAK,CAACC,8BAAkB,CAACH,UAAU,EAAE,UACtD,IAAI,CAACI,cAAc,CAAC9Q,KAAKC,KAAKE,UAAUwB,OAAOC;IAEnD;IAEA,MAAckP,eACZ9Q,GAAoB,EACpBC,GAAqB,EACrBE,QAAgB,EAChBwB,KAAyB,EACzBC,UAA4B,EACL;QACvB,IAAIjE,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIc,MACR,+DADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACA,+CAA+C;QACjD,OAAO;YACL,4EAA4E;YAC5E,8DAA8D;YAC9D,4HAA4H;YAC5HiD,WAAWmP,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;YAEnD,IAAI,IAAI,CAACzC,kBAAkB,CAACC,GAAG,IAAI3M,WAAW8H,SAAS,EAAE;gBACvD,OAAOsH,IAAAA,+BAAiB,EACtBhR,KACAC,KACAE,UACAwB,OACA,kEAAkE;gBAClE,oEAAoE;gBACpE,MACAC,YACA,IAAI,CAACqP,2BAA2B,IAChC,OACA;oBACEC,SAAS,IAAI,CAACA,OAAO;gBACvB;YAEJ;YAEA,qEAAqE;YACrE,oEAAoE;YAEpE,OAAOC,IAAAA,kCAAmB,EACxBnR,IAAI0B,eAAe,EACnBzB,IAAIsD,gBAAgB,EACpBpD,UACAwB,OACAC,YACA;gBACEsP,SAAS,IAAI,CAACA,OAAO;gBACrB7H,cAAc,IAAI,CAAC9I,UAAU,CAAC8I,YAAY;gBAC1C+H,cAAc,IAAI,CAAC7G,aAAa,CAAC6G,YAAY,IAAIlO;YACnD,GACA;gBACEI,YAAY;gBACZ+N,aAAazP,WAAWyP,WAAW;gBACnCC,+BAA+BjR,IAAAA,2BAAc,EAC3CL,KACA;YAEJ;QAEJ;IACF;IAEA,MAAgByC,eACdzC,GAAoB,EACpBC,GAAqB,EACrBuB,YAA2D,EAC3DW,kBAAyD,EAOxD;QACD,IAAIxE,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIc,MACR,mEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO;YACL,MAAM,EAAE8D,cAAc,EAAE8O,kBAAkB,EAAEC,kBAAkB,EAAE,GAC9D3Q,QAAQ;YAEV,MAAM4Q,oBAAoB,OACxBC,QACAC;gBAEA,IAAID,OAAOpK,GAAG,KAAKtH,IAAIsH,GAAG,EAAE;oBAC1B,MAAM,qBAA+D,CAA/D,IAAI3I,MAAM,CAAC,kDAAkD,CAAC,GAA9D,qBAAA;+BAAA;oCAAA;sCAAA;oBAA8D;gBACtE;gBAEA,IAAI,CAAC,IAAI,CAACiT,mBAAmB,EAAE;oBAC7B,MAAM,qBAAkD,CAAlD,IAAIjT,MAAM,CAAC,qCAAqC,CAAC,GAAjD,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiD;gBACzD;gBAEA,MAAM,IAAI,CAACiT,mBAAmB,CAACF,QAAQC;gBACvC;YACF;YAEA,MAAM,EAAEE,UAAU,EAAErO,IAAI,EAAE,GAAGhC;YAE7B,MAAMsQ,gBAAgBD,aAClB,MAAMN,mBAAmB/N,QACzB,MAAMgO,mBACJhO,MACAxD,IAAI0B,eAAe,EACnBzB,IAAIsD,gBAAgB,EACpBkO;YAGN,OAAOhP,eAAeqP,eAAetQ,cAAc,IAAI,CAACjB,UAAU,EAAE;gBAClEkI,OAAO,IAAI,CAAC7G,UAAU,CAACC,GAAG;gBAC1BM;YACF;QACF;IACF;IAEU4P,YAAY5R,QAAgB,EAAEgP,OAAkB,EAAU;QAClE,OAAO4C,IAAAA,oBAAW,EAChB5R,UACA,IAAI,CAACY,OAAO,EACZoO,SACA,IAAI,CAACb,kBAAkB,CAACC,GAAG;IAE/B;IAEA,MAAgByD,oBACdC,GAAsD,EACtDC,gBAAyB,EACzB;QACA,MAAMnN,qBAAqB,IAAI,CAACC,qBAAqB,MAAM,EAAE;QAC7D,IAAID,mBAAmBoN,MAAM,EAAE;YAC7B,MAAM3M,WAAW,IAAI,CAAC4M,mBAAmB,CAACH,IAAI9R,QAAQ;YACtD,MAAMuJ,YAAYlL,MAAMC,OAAO,CAAC+G;YAEhC,IAAIL,OAAO8M,IAAI9R,QAAQ;YACvB,IAAIuJ,WAAW;gBACb,yEAAyE;gBACzEvE,OAAOK,QAAQ,CAAC,EAAE;YACpB;YAEA,KAAK,MAAMP,qBAAqBF,mBAAoB;gBAClD,IAAIE,sBAAsBE,MAAM;oBAC9B,MAAM,IAAI,CAACG,eAAe,CAAC;wBACzBtF,KAAKiS,IAAIjS,GAAG;wBACZC,KAAKgS,IAAIhS,GAAG;wBACZ0B,OAAOsQ,IAAItQ,KAAK;wBAChB4D,QAAQ0M,IAAIrQ,UAAU,CAAC2D,MAAM;wBAC7BJ;wBACAK;oBACF;oBACA,OAAO;gBACT;YACF;QACF;QAEA,OAAO,KAAK,CAACwM,oBAAoBC,KAAKC;IACxC;IAEA,MAAgBG,mBAAmB,EACjCC,MAAM,EACNnN,IAAI,EACJxD,KAAK,EACL4D,MAAM,EACNmE,SAAS,EACTpC,GAAG,EAaJ,EAAwC;QACvC,OAAOqJ,IAAAA,iBAAS,IAAGC,KAAK,CACtBC,8BAAkB,CAACwB,kBAAkB,EACrC;YACEE,UAAU;YACVC,YAAY;gBACV,cAAc9I,YAAY+I,IAAAA,0BAAgB,EAACtN,QAAQA;YACrD;QACF,GACA,IACE,IAAI,CAACuN,sBAAsB,CAAC;gBAC1BJ;gBACAnN;gBACAxD;gBACA4D;gBACAmE;gBACApC;YACF;IAEN;IAEA,MAAcoL,uBAAuB,EACnCJ,MAAM,EACNnN,IAAI,EACJxD,KAAK,EACL4D,MAAM,EACNmE,SAAS,EACTpC,KAAKqL,IAAI,EAQV,EAAwC;QACvC,MAAMC,YAAsB;YAACzN;SAAK;QAClC,IAAIxD,MAAMkR,GAAG,EAAE;YACb,yCAAyC;YACzCD,UAAUE,OAAO,CACf,AAACpJ,CAAAA,YAAY+I,IAAAA,0BAAgB,EAACtN,QAAQ4N,IAAAA,oCAAiB,EAAC5N,KAAI,IAAK;QAErE;QAEA,IAAImN,QAAQ;YACVM,UAAUE,OAAO,IACZF,UAAU5I,GAAG,CAAC,CAACgJ,OAAS,CAAC,CAAC,EAAEV,SAASU,SAAS,MAAM,KAAKA,MAAM;QAEtE;QAEA,KAAK,MAAMC,YAAYL,UAAW;YAChC,IAAI;gBACF,MAAMM,aAAa,MAAMzJ,IAAAA,8BAAc,EAAC;oBACtC1I,SAAS,IAAI,CAACA,OAAO;oBACrBoE,MAAM8N;oBACNvJ;oBACAjB,OAAO,IAAI,CAACA,KAAK;oBACjBC,YAAY,IAAI,CAACA,UAAU;gBAC7B;gBAEA,IACE4J,UACA,OAAOY,WAAWC,SAAS,KAAK,YAChC,CAACF,SAAS7S,UAAU,CAAC,CAAC,CAAC,EAAEkS,OAAO,CAAC,CAAC,KAClCW,aAAa,CAAC,CAAC,EAAEX,QAAQ,EACzB;oBAGA;gBACF;gBAEA,OAAO;oBACLY;oBACAvR,OAAO;wBACL,GAAI,CAAC,IAAI,CAACC,UAAU,CAACoJ,qBAAqB,IAC1CkI,WAAWE,cAAc,GACpB;4BACCP,KAAKlR,MAAMkR,GAAG;wBAChB,IACAlR,KAAK;wBACT,iCAAiC;wBACjC,GAAI,AAAC+H,CAAAA,YAAY,CAAC,IAAInE,MAAK,KAAM,CAAC,CAAC;oBACrC;gBACF;YACF,EAAE,OAAO1B,KAAK;gBACZ,yDAAyD;gBACzD,wBAAwB;gBACxB,IAAI,CAAEA,CAAAA,eAAewP,wBAAiB,AAAD,GAAI;oBACvC,MAAMxP;gBACR;YACF;QACF;QACA,OAAO;IACT;IAEUyP,sBAAoD;QAC5D,OAAOlF,IAAAA,kCAAY,EACjBxD,IAAAA,UAAI,EAAC,IAAI,CAAC7J,OAAO,EAAE,UAAUwS,6BAAkB,GAAG;IAEtD;IA4PA,0DAA0D;IAChDpN,0BACRqN,IAAc,EACdC,KAA0E,EACpE;QACN,MAAM,qBAEL,CAFK,IAAI9U,MACR,sFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,0DAA0D;IAC1D,MAAgB+U,WAAWC,KAM1B,EAAiB;QAChB,MAAM,qBAEL,CAFK,IAAIhV,MACR,uEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA;;;;;GAKC,GACD,MAAgBqH,iBACdhG,GAAoB,EACpBC,GAAqB,EACrB0B,KAAqB,EACrBkD,KAAyB,EACP;QAClB,OAAO,IAAI,CAACmL,MAAM,CAAChQ,KAAKC,KAAK0B,OAAOkD;IACtC;IAEUyI,qBAA8B;QACtC,OAAOsG,qBAAM;IACf;IAEUC,aACR7T,GAAsC,EACrB;QACjB,OAAO,CAAEA,CAAAA,eAAe8T,qBAAe,AAAD,IAAK,IAAIA,qBAAe,CAAC9T,OAAOA;IACxE;IAEU+T,aACR9T,GAAsC,EACpB;QAClB,OAAO,CAAEA,CAAAA,eAAe+T,sBAAgB,AAAD,IAAK,IAAIA,sBAAgB,CAAC/T,OAAOA;IAC1E;IAEOgU,oBAAwC;QAC7C,MAAMrH,UAAU,IAAI,CAACsH,kBAAkB;QACvC,IAAI,IAAI,CAAC3J,aAAa,CAACC,qBAAqB,EAAE;YAC5C,MAAM,EAAE2J,sBAAsB,EAAE,GAC9B,sHAAsH;YACtHtT,QAAQ;YACV,OAAOsT,uBAAuBvH;QAChC;QACA,OAAOA;IACT;IAEQsH,qBAAyC;QAC/C,4EAA4E;QAC5E,2EAA2E;QAC3E,sEAAsE;QACtE,uEAAuE;QACvE,IAAI,CAACnJ,OAAO,GAAGpB,KAAK,CAAC,CAAC9F;YACpBtE,QAAQC,KAAK,CAAC,4BAA4BqE;QAC5C;QAEA,MAAM+I,UAAU,KAAK,CAACqH;QAEtB,OAAO,CAACjU,KAAKC,KAAKC,YAChB0M,QAAQ,IAAI,CAACiH,YAAY,CAAC7T,MAAM,IAAI,CAAC+T,YAAY,CAAC9T,MAAMC;IAC5D;IAEA,MAAa8C,WAAW,EACtBoR,OAAO,EACPC,iBAAiB,EACjBC,IAAI,EAKL,EAAE;QACD,MAAMC,SAASC,IAAAA,uCAA0B,EAAC;YACxClN,KAAK8M;YACLrM,SAASsM;QACX;QAEA,MAAMzH,UAAU,IAAI,CAACqH,iBAAiB;QACtC,MAAMrH,QACJ,IAAIkH,qBAAe,CAACS,OAAOvU,GAAG,GAC9B,IAAIgU,sBAAgB,CAACO,OAAOtU,GAAG;QAEjC,MAAMsU,OAAOtU,GAAG,CAACwU,WAAW;QAE5B,IACEF,OAAOtU,GAAG,CAACyU,SAAS,CAAC,sBAAsB,iBAC3CH,OAAOtU,GAAG,CAACQ,UAAU,KAAK,OAC1B,CAAE8T,CAAAA,OAAOtU,GAAG,CAACQ,UAAU,KAAK,OAAO6T,KAAKK,sBAAsB,AAAD,GAC7D;YACA,MAAM,qBAAsD,CAAtD,IAAIhW,MAAM,CAAC,iBAAiB,EAAE4V,OAAOtU,GAAG,CAACQ,UAAU,EAAE,GAArD,qBAAA;uBAAA;4BAAA;8BAAA;YAAqD;QAC7D;IACF;IAEA,MAAaqE,OACX9E,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBwB,KAA0B,EAC1BzB,SAAkC,EAClC0U,WAAW,KAAK,EACD;QACf,OAAO,KAAK,CAAC9P,OACX,IAAI,CAAC+O,YAAY,CAAC7T,MAClB,IAAI,CAAC+T,YAAY,CAAC9T,MAClBE,UACAwB,OACAzB,WACA0U;IAEJ;IAEA,MAAaC,aACX7U,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBwB,KAAsB,EACE;QACxB,OAAO,KAAK,CAACkT,aACX,IAAI,CAAChB,YAAY,CAAC7T,MAClB,IAAI,CAAC+T,YAAY,CAAC9T,MAClBE,UACAwB;IAEJ;IAEA,MAAgBmT,0BACd7C,GAAsD,EACtDpO,GAAiB,EACjB;QACA,MAAM,EAAE7D,GAAG,EAAEC,GAAG,EAAE0B,KAAK,EAAE,GAAGsQ;QAC5B,MAAM8C,QAAQ9U,IAAIQ,UAAU,KAAK;QAEjC,IAAIsU,SAAS,IAAI,CAACzG,kBAAkB,CAACC,GAAG,EAAE;YACxC,IAAI,IAAI,CAAC3M,UAAU,CAACC,GAAG,EAAE;gBACvB,MAAM,IAAI,CAAC6R,UAAU,CAAC;oBACpBvO,MAAM6P,2CAAgC;oBACtCC,YAAY;oBACZ3N,KAAKtH,IAAIsH,GAAG;gBACd,GAAGqC,KAAK,CAAC,KAAO;YAClB;YAEA,IACE,IAAI,CAAC3E,qBAAqB,GAAGkQ,QAAQ,CAACF,2CAAgC,GACtE;gBACA,MAAM,IAAI,CAAC1P,eAAe,CAAC;oBACzBtF;oBACAC;oBACA0B,OAAOA,SAAS,CAAC;oBACjB4D,QAAQ,CAAC;oBACTJ,MAAM6P,2CAAgC;oBACtCxP,UAAU;gBACZ;gBACA,OAAO;YACT;QACF;QACA,OAAO,KAAK,CAACsP,0BAA0B7C,KAAKpO;IAC9C;IAEA,MAAawC,YACXxC,GAAiB,EACjB7D,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBwB,KAA0B,EAC1BwT,UAAoB,EACL;QACf,OAAO,KAAK,CAAC9O,YACXxC,KACA,IAAI,CAACgQ,YAAY,CAAC7T,MAClB,IAAI,CAAC+T,YAAY,CAAC9T,MAClBE,UACAwB,OACAwT;IAEJ;IAEA,MAAaC,kBACXvR,GAAiB,EACjB7D,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBwB,KAAsB,EACE;QACxB,OAAO,KAAK,CAACyT,kBACXvR,KACA,IAAI,CAACgQ,YAAY,CAAC7T,MAClB,IAAI,CAAC+T,YAAY,CAAC9T,MAClBE,UACAwB;IAEJ;IAEA,MAAaJ,UACXvB,GAAsC,EACtCC,GAAsC,EACtCC,SAAkC,EAClCiV,UAAoB,EACL;QACf,OAAO,KAAK,CAAC5T,UACX,IAAI,CAACsS,YAAY,CAAC7T,MAClB,IAAI,CAAC+T,YAAY,CAAC9T,MAClBC,WACAiV;IAEJ;IAEUE,wBAAmD;QAC3D,IAAI,IAAI,CAAC/U,WAAW,EAAE;YACpB,OAAO;QACT,OAAO;YACL,MAAMgV,WAA+BzU,QAAQ,IAAI,CAAC8J,sBAAsB;YACxE,OAAO2K;QACT;IACF;IAEA,yDAAyD,GACzD,MAAgB3O,gBAA4D;YAEvD2O;QADnB,MAAMA,WAAW,IAAI,CAACD,qBAAqB;QAC3C,MAAM3O,aAAa4O,6BAAAA,uBAAAA,SAAU5O,UAAU,qBAApB4O,oBAAsB,CAAC,IAAI;QAC9C,IAAI,CAAC5O,YAAY;YACf,MAAM6O,mBAAmB,MAAM,IAAI,CAACC,kBAAkB;YAEtD,IAAID,kBAAkB;oBAGhBA;gBAFJ,OAAO;oBACL1Q,OAAO9F,IAAAA,iDAAyB,EAC9BwW,EAAAA,2BAAAA,iBAAiBE,MAAM,qBAAvBF,yBAAyB7W,QAAQ,KAAI;wBACnC;4BAAEgX,QAAQ;4BAAMC,gBAAgB;wBAAU;qBAC3C;oBAEHxQ,MAAM;gBACR;YACF;YAEA;QACF;QAEA,OAAO;YACLN,OAAOzG,qBAAqBsI;YAC5BvB,MAAM;QACR;IACF;IAEUH,wBAAkC;QAC1C,MAAMsQ,WAAW,IAAI,CAACD,qBAAqB;QAC3C,IAAI,CAACC,UAAU;YACb,OAAO,EAAE;QACX;QAEA,OAAO1N,OAAO4D,IAAI,CAAC8J,SAASM,SAAS;IACvC;IAEA;;;;GAIC,GACD,AAAUC,oBAAoBtQ,MAI7B,EAMQ;QACP,MAAM+P,WAAW,IAAI,CAACD,qBAAqB;QAC3C,IAAI,CAACC,UAAU;YACb,OAAO;QACT;QAEA,IAAIQ;QAEJ,IAAI;YACFA,YAAYC,IAAAA,wCAAmB,EAAChD,IAAAA,oCAAiB,EAACxN,OAAOJ,IAAI;QAC/D,EAAE,OAAOtB,KAAK;YACZ,OAAO;QACT;QAEA,IAAImS,WAAWzQ,OAAOmB,UAAU,GAC5B4O,SAAS5O,UAAU,CAACoP,UAAU,GAC9BR,SAASM,SAAS,CAACE,UAAU;QAEjC,IAAI,CAACE,UAAU;YACb,IAAI,CAACzQ,OAAOmB,UAAU,EAAE;gBACtB,MAAM,IAAI2M,wBAAiB,CAACyC;YAC9B;YACA,OAAO;QACT;QAEA,OAAO;YACLG,MAAMD,SAASC,IAAI;YACnBC,OAAOF,SAASG,KAAK,CAACnM,GAAG,CAAC,CAACoM,OAASxL,IAAAA,UAAI,EAAC,IAAI,CAAC7J,OAAO,EAAEqV;YACvDC,MAAM,AAACL,CAAAA,SAASK,IAAI,IAAI,EAAE,AAAD,EAAGrM,GAAG,CAAC,CAACsM,UAAa,CAAA;oBAC5C,GAAGA,OAAO;oBACVC,UAAU3L,IAAAA,UAAI,EAAC,IAAI,CAAC7J,OAAO,EAAEuV,QAAQC,QAAQ;gBAC/C,CAAA;YACAC,QACER,SAASQ,MAAM,IACfR,SAASQ,MAAM,CAACxM,GAAG,CAAC,CAACsM;gBACnB,OAAO;oBACL,GAAGA,OAAO;oBACVC,UAAU3L,IAAAA,UAAI,EAAC,IAAI,CAAC7J,OAAO,EAAEuV,QAAQC,QAAQ;gBAC/C;YACF;YACF3Y,KAAKoY,SAASpY,GAAG;QACnB;IACF;IAEA,MAAc4X,qBAAqB;QACjC,IAAI,CAAC7X,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC7B,IAAI,CAAC,IAAI,CAAC0C,UAAU,CAACqI,YAAY,CAAC6N,cAAc,EAAE;gBAChD;YACF;YAEA,IAAI;oBAOAC;gBANF,MAAMA,kBAAkB,IAAI,CAAC9U,UAAU,CAACC,GAAG,GACvC,CAAC,IACDhB,QAAQ+J,IAAAA,UAAI,EAAC,IAAI,CAAC7J,OAAO,EAAE,UAAU4V,oCAAyB;gBAElE,IACE,IAAI,CAAC/U,UAAU,CAACC,GAAG,KACnB6U,oCAAAA,6BAAAA,gBAAiBd,SAAS,qBAA1Bc,0BAA4B,CAAC,eAAe,GAC5C;oBACA,uDAAuD;oBACvD,OAAO7V,QAAQ+J,IAAAA,UAAI,EAAC,IAAI,CAAC7J,OAAO,EAAE,UAAU;gBAC9C;YACF,EAAE,OAAO8C,KAAK;gBACZ,IACEuE,IAAAA,gBAAO,EAACvE,QACRA,IAAIwE,IAAI,KAAK,YACbxE,IAAIwE,IAAI,KAAK,oBACb;oBACA,MAAMxE;gBACR;YACF;QACF;IACF;IAEA;;;;GAIC,GACD,MAAgB+S,cAAczW,QAAgB,EAAoB;QAChE,MAAM9B,OAAO,IAAI,CAACwX,mBAAmB,CAAC;YAAE1Q,MAAMhF;YAAUuG,YAAY;QAAK;QACzE,MAAM+P,iBAAiB,MAAM,IAAI,CAACjB,kBAAkB;QAEpD,IAAI,CAACnX,QAAQoY,gBAAgB;YAC3B,OAAO;QACT;QACA,OAAO7S,QAAQvF,QAAQA,KAAK6X,KAAK,CAAC/D,MAAM,GAAG;IAC7C;IAEA;;;;GAIC,GACD,MAAgB9K,iBAAiBsL,IAAa,EAAE,CAAC;IACjD,MAAgBkE,mBAAmBC,OAIlC,EAAE,CAAC;IAEJ;;;;;GAKC,GACD,MAAgBvP,cAAchC,MAM7B,EAAE;QACD,IAAI5H,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIc,MACR,kEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,0DAA0D;QAC1D,IACEoY,IAAAA,mCAAyB,EAACxR,OAAOiC,OAAO,EAAE,IAAI,CAAC5F,UAAU,CAACoV,YAAY,EACnEC,oBAAoB,EACvB;YACA,OAAO;gBACLxP,UAAU,IAAIyP,SAAS,MAAM;oBAAEnP,SAAS;wBAAE,qBAAqB;oBAAI;gBAAE;YACvE;QACF;QAEA,IAAIT;QAEJ,IAAI,IAAI,CAAC/G,UAAU,CAAC4W,0BAA0B,EAAE;YAC9C7P,MAAMjH,IAAAA,2BAAc,EAACkF,OAAOiC,OAAO,EAAE;QACvC,OAAO;YACL,mEAAmE;YACnE,MAAM7F,QAAQyV,IAAAA,mCAAsB,EAAC7R,OAAOgB,MAAM,CAAC5E,KAAK,EAAE0V,QAAQ;YAClE,MAAM/E,SAASjS,IAAAA,2BAAc,EAACkF,OAAOiC,OAAO,EAAE;YAE9CF,MAAM,GAAGjH,IAAAA,2BAAc,EAACkF,OAAOiC,OAAO,EAAE,gBAAgB,GAAG,EACzD,IAAI,CAAC8P,aAAa,IAAI,YACvB,CAAC,EAAE,IAAI,CAACC,IAAI,GAAGjF,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAK/M,OAAOgB,MAAM,CAACpG,QAAQ,GACjEwB,QAAQ,CAAC,CAAC,EAAEA,OAAO,GAAG,IACtB;QACJ;QAEA,IAAI,CAAC2F,IAAIlH,UAAU,CAAC,SAAS;YAC3B,MAAM,qBAEL,CAFK,IAAIzB,MACR,qFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMwG,OAGF,CAAC;QAEL,MAAMuB,aAAa,MAAM,IAAI,CAACC,aAAa;QAC3C,IAAI,CAACD,YAAY;YACf,OAAO;gBAAE8B,UAAU;YAAM;QAC3B;QACA,IAAI,CAAE,MAAM,IAAI,CAACoO,aAAa,CAAClQ,WAAWvB,IAAI,GAAI;YAChD,OAAO;gBAAEqD,UAAU;YAAM;QAC3B;QAEA,MAAM,IAAI,CAACnB,gBAAgB,CAAC9B,OAAOiC,OAAO,CAACF,GAAG;QAC9C,MAAMkQ,iBAAiB,IAAI,CAAC3B,mBAAmB,CAAC;YAC9C1Q,MAAMuB,WAAWvB,IAAI;YACrBuB,YAAY;QACd;QAEA,MAAM+Q,SAAS,AAAClS,CAAAA,OAAOiC,OAAO,CAACiQ,MAAM,IAAI,KAAI,EAAGC,WAAW;QAC3D,MAAMC,cAAc;YAClB5P,SAASxC,OAAOiC,OAAO,CAACO,OAAO;YAC/B0P;YACAlX,YAAY;gBACVqX,UAAU,IAAI,CAACrX,UAAU,CAACqX,QAAQ;gBAClClT,MAAM,IAAI,CAACnE,UAAU,CAACmE,IAAI;gBAC1BmT,eAAe,IAAI,CAACtX,UAAU,CAACsX,aAAa;gBAC5CjP,cAAc,IAAI,CAACrI,UAAU,CAACqI,YAAY;YAC5C;YACAtB,KAAKA;YACLnC;YACAzE,MACE+W,WAAW,SAASA,WAAW,SAC1BpX,IAAAA,2BAAc,EAACkF,OAAOiC,OAAO,EAAE,kBAChCtE;YAEN4U,QAAQC,IAAAA,mCAAsB,EAACxS,OAAOkC,QAAQ,CAAClE,gBAAgB;YAC/DiN,WAAW,IAAI,CAACC,YAAY;QAC9B;QACA,IAAItJ;QAIJ,qDAAqD;QACrD,8DAA8D;QAC9D,6DAA6D;QAC7D,6DAA6D;QAC7D,uBAAuB;QACvB,IAAI,CAACqQ,gBAAgB;YACnB,IAAIjC;YACJA,mBAAmB,MAAM,IAAI,CAACC,kBAAkB;YAEhD,IAAI,CAACD,kBAAkB;gBACrB,MAAM,IAAIyC,8BAAuB;YACnC;YACA,MAAMC,YACJ1C,iBAAiBtX,OAAO,IAAIsX;YAE9B,MAAM2C,iBACJ,CAAC;gBAAC;gBAAQ;aAAM,CAAChD,QAAQ,CAAC3P,OAAOiC,OAAO,CAACiQ,MAAM,KAC/C7T,QAAQ+T,YAAYjX,IAAI;YAE1B,IAAI;gBACFyG,SAAS,MAAM8Q,UAAU;oBACvBrL,SAAS2I,iBAAiB7O,UAAU,IAAI6O;oBACxC/N,SAAS;wBACP,GAAGmQ,WAAW;wBACdjX,MAAMwX,iBACFP,YAAYjX,IAAI,CAACyX,eAAe,KAChCjV;oBACN;oBACAiC,MAAM;gBACR;YACF,SAAU;gBACR,IAAI+S,gBAAgB;oBAClBP,YAAYjX,IAAI,CAAC0X,QAAQ;gBAC3B;YACF;QACF,OAAO;YACL,MAAM,EAAEC,GAAG,EAAE,GAAGxX,QAAQ;YAExBsG,SAAS,MAAMkR,IAAI;gBACjBtX,SAAS,IAAI,CAACA,OAAO;gBACrBkV,MAAMuB,eAAevB,IAAI;gBACzBC,OAAOsB,eAAetB,KAAK;gBAC3BoC,mBAAmBd;gBACnBhQ,SAASmQ;gBACTY,UAAU;gBACVC,WAAWjT,OAAOiT,SAAS;YAC7B;QACF;QAEA,IAAI,CAAC,IAAI,CAAC5W,UAAU,CAACC,GAAG,EAAE;YACxBsF,OAAOqJ,SAAS,CAAC7G,KAAK,CAAC,CAACnK;gBACtBD,QAAQC,KAAK,CAAC,CAAC,sCAAsC,CAAC,EAAEA;YAC1D;QACF;QAEA,IAAI,CAAC2H,QAAQ;YACX,IAAI,CAAC5F,SAAS,CAACgE,OAAOiC,OAAO,EAAEjC,OAAOkC,QAAQ,EAAElC,OAAOgB,MAAM;YAC7D,OAAO;gBAAEiC,UAAU;YAAK;QAC1B;QAEA,sDAAsD;QACtD,IAAIrB,OAAOM,QAAQ,CAACM,OAAO,CAAC0Q,GAAG,CAAC,eAAe;YAC7C,MAAMC,UAAUvR,OAAOM,QAAQ,CAACM,OAAO,CACpC4Q,YAAY,GACZC,OAAO,CAAC,CAACC,sBACRC,IAAAA,0BAAkB,EAACD;YAGvB,2BAA2B;YAC3B1R,OAAOM,QAAQ,CAACM,OAAO,CAACgR,MAAM,CAAC;YAE/B,mCAAmC;YACnC,KAAK,MAAMC,UAAUN,QAAS;gBAC5BvR,OAAOM,QAAQ,CAACM,OAAO,CAACkR,MAAM,CAAC,cAAcD;YAC/C;YAEA,+BAA+B;YAC/BhV,IAAAA,2BAAc,EAACuB,OAAOiC,OAAO,EAAE,oBAAoBkR;QACrD;QAEA,OAAOvR;IACT;IAmHU0G,uBAA0C;QAClD,IAAI,IAAI,CAACqL,sBAAsB,EAAE;YAC/B,OAAO,IAAI,CAACA,sBAAsB;QACpC;QAEA,IAAI,CAACA,sBAAsB,GAAG9K,IAAAA,kCAAY,EACxCxD,IAAAA,UAAI,EAAC,IAAI,CAAC7J,OAAO,EAAEoY,6BAAkB;QAGvC,OAAO,IAAI,CAACD,sBAAsB;IACpC;IAEUnP,oBAAyD;QACjE,OAAO4G,IAAAA,iBAAS,IAAGC,KAAK,CACtBC,8BAAkB,CAAC9G,iBAAiB,EACpC,IAAMqE,IAAAA,kCAAY,EAACxD,IAAAA,UAAI,EAAC,IAAI,CAAC7J,OAAO,EAAEqY,0BAAe;IAEzD;IAEUC,kBACRrZ,GAAoB,EACpBE,SAAiC,EACjCoZ,YAAsB,EACtB;YAEiBtZ;QADjB,6BAA6B;QAC7B,MAAMuZ,WAAWvZ,EAAAA,+BAAAA,IAAI+H,OAAO,CAAC,oBAAoB,qBAAhC/H,6BAAkCkV,QAAQ,CAAC,YACxD,UACA;QAEJ,4DAA4D;QAC5D,MAAMtO,UACJ,IAAI,CAAC0Q,aAAa,IAAI,IAAI,CAACC,IAAI,GAC3B,GAAGgC,SAAS,GAAG,EAAE,IAAI,CAACjC,aAAa,CAAC,CAAC,EAAE,IAAI,CAACC,IAAI,GAAGvX,IAAIsH,GAAG,EAAE,GAC5D,IAAI,CAAC/G,UAAU,CAACqI,YAAY,CAAC4Q,eAAe,GAC1C,CAAC,QAAQ,EAAExZ,IAAI+H,OAAO,CAAC0R,IAAI,IAAI,cAAczZ,IAAIsH,GAAG,EAAE,GACtDtH,IAAIsH,GAAG;QAEftD,IAAAA,2BAAc,EAAChE,KAAK,WAAW4G;QAC/B5C,IAAAA,2BAAc,EAAChE,KAAK,aAAa;YAAE,GAAGE,UAAUyB,KAAK;QAAC;QACtDqC,IAAAA,2BAAc,EAAChE,KAAK,gBAAgBuZ;QAEpC,IAAI,CAACD,cAAc;YACjBtV,IAAAA,2BAAc,EAAChE,KAAK,gBAAgB0Z,IAAAA,6BAAgB,EAAC1Z,IAAI0B,eAAe;QAC1E;IACF;IAEA,MAAgB4D,gBAAgBC,MAU/B,EAAoC;QACnC,IAAI5H,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIc,MACR,wGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAIgb;QAEJ,MAAM,EAAEhY,KAAK,EAAEwD,IAAI,EAAEN,KAAK,EAAE,GAAGU;QAE/B,IAAI,CAACV,OACH,MAAM,IAAI,CAACgS,kBAAkB,CAAC;YAC5B1R;YACAK,UAAUD,OAAOC,QAAQ;YACzB8B,KAAK/B,OAAOvF,GAAG,CAACsH,GAAG;QACrB;QACFqS,WAAW,IAAI,CAAC9D,mBAAmB,CAAC;YAClC1Q;YACAuB,YAAY;QACd;QAEA,IAAI,CAACiT,UAAU;YACb,OAAO;QACT;QAEA,6DAA6D;QAC7D,MAAMC,oBAAoBvZ,IAAAA,2BAAc,EAACkF,OAAOvF,GAAG,EAAE;QACrD,MAAM6Z,aAAa,IAAIC,IACrBzZ,IAAAA,2BAAc,EAACkF,OAAOvF,GAAG,EAAE,cAAc,KACzC;QAEF,MAAM+Z,cAAc3C,IAAAA,mCAAsB,EAAC;YACzC,GAAGxP,OAAOoS,WAAW,CAACH,WAAWI,YAAY,CAAC;YAC9C,GAAGtY,KAAK;YACR,GAAG4D,OAAOA,MAAM;QAClB,GAAG8R,QAAQ;QAEX,IAAIuC,mBAAmB;YACrBrU,OAAOvF,GAAG,CAAC+H,OAAO,CAAC,gBAAgB,GAAG;QACxC;QACA8R,WAAW1J,MAAM,GAAG4J;QACpB,MAAMzS,MAAMuS,WAAWxC,QAAQ;QAE/B,IAAI,CAAC/P,IAAIlH,UAAU,CAAC,SAAS;YAC3B,MAAM,qBAEL,CAFK,IAAIzB,MACR,qFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAE0Z,GAAG,EAAE,GAAGxX,QAAQ;QACxB,MAAMsG,SAAS,MAAMkR,IAAI;YACvBtX,SAAS,IAAI,CAACA,OAAO;YACrBkV,MAAM0D,SAAS1D,IAAI;YACnBC,OAAOyD,SAASzD,KAAK;YACrBoC,mBAAmBqB;YACnBnS,SAAS;gBACPO,SAASxC,OAAOvF,GAAG,CAAC+H,OAAO;gBAC3B0P,QAAQlS,OAAOvF,GAAG,CAACyX,MAAM;gBACzBlX,YAAY;oBACVqX,UAAU,IAAI,CAACrX,UAAU,CAACqX,QAAQ;oBAClClT,MAAM,IAAI,CAACnE,UAAU,CAACmE,IAAI;oBAC1BmT,eAAe,IAAI,CAACtX,UAAU,CAACsX,aAAa;gBAC9C;gBACAvQ;gBACAnC,MAAM;oBACJ8Q,MAAM1Q,OAAOJ,IAAI;oBACjB,GAAII,OAAOA,MAAM,IAAI;wBAAEA,QAAQA,OAAOA,MAAM;oBAAC,CAAC;gBAChD;gBACA7E,MAAML,IAAAA,2BAAc,EAACkF,OAAOvF,GAAG,EAAE;gBACjC8X,QAAQC,IAAAA,mCAAsB,EAACxS,OAAOtF,GAAG,CAACsD,gBAAgB;gBAC1DiN,WAAW,IAAI,CAACC,YAAY;YAC9B;YACA8H,UAAU;YACV2B,SAAS3U,OAAO2U,OAAO;YACvB1B,WAAWjT,OAAOiT,SAAS;YAC3BnV,kBACE,AAAC8W,WAAmBC,kBAAkB,IACtC/Z,IAAAA,2BAAc,EAACkF,OAAOvF,GAAG,EAAE;YAC7Bqa,0BAA0Bha,IAAAA,2BAAc,EACtCkF,OAAOvF,GAAG,EACV;QAEJ;QAEA,IAAImH,OAAOmT,YAAY,EAAE;YACvB/U,OAAOvF,GAAG,CAACsa,YAAY,GAAGnT,OAAOmT,YAAY;QAC/C;QAEA,IAAI,CAAC/U,OAAOtF,GAAG,CAACQ,UAAU,IAAI8E,OAAOtF,GAAG,CAACQ,UAAU,GAAG,KAAK;YACzD8E,OAAOtF,GAAG,CAACQ,UAAU,GAAG0G,OAAOM,QAAQ,CAACQ,MAAM;YAC9C1C,OAAOtF,GAAG,CAACsa,aAAa,GAAGpT,OAAOM,QAAQ,CAAC+S,UAAU;QACvD;QAEA,8CAA8C;QAE9CrT,OAAOM,QAAQ,CAACM,OAAO,CAAC0S,OAAO,CAAC,CAAC/X,OAAOiF;YACtC,yDAAyD;YACzD,IAAIA,IAAI+S,WAAW,OAAO,cAAc;gBACtC,qFAAqF;gBACrF,KAAK,MAAM1B,UAAUF,IAAAA,0BAAkB,EAACpW,OAAQ;oBAC9C6C,OAAOtF,GAAG,CAAC0a,YAAY,CAAChT,KAAKqR;gBAC/B;YACF,OAAO;gBACLzT,OAAOtF,GAAG,CAAC0a,YAAY,CAAChT,KAAKjF;YAC/B;QACF;QAEA,MAAM,EAAEa,gBAAgB,EAAE,GAAGgC,OAAOtF,GAAG;QACvC,IAAIkH,OAAOM,QAAQ,CAAC/G,IAAI,EAAE;YACxB,MAAMwH,IAAAA,gCAAkB,EAACf,OAAOM,QAAQ,CAAC/G,IAAI,EAAE6C;QACjD,OAAO;YACLA,iBAAiB4E,GAAG;QACtB;QAEA,OAAOhB;IACT;IAEA,IAAc0D,gBAAwB;QACpC,IAAI,IAAI,CAAC+P,cAAc,EAAE;YACvB,OAAO,IAAI,CAACA,cAAc;QAC5B;QACA,MAAM/P,gBAAgBD,IAAAA,UAAI,EAAC,IAAI,CAAC7J,OAAO,EAAE8Z,2BAAgB;QACzD,IAAI,CAACD,cAAc,GAAG/P;QACtB,OAAOA;IACT;IAEA,MAAgBiQ,2BACdnI,IAAa,EAC6B;QAC1C,uEAAuE;QACvE,sBAAsB;QACtB,OAAO;IACT;IAEA,MAAgBjN,8BACd,GAAGqV,IAAqD,EACxD;QACA,MAAM,KAAK,CAACrV,iCAAiCqV;QAE7C,uFAAuF;QACvF,IAAI,CAAC,IAAI,CAACnZ,UAAU,CAACC,GAAG,EAAE;YACxB,IAAI,CAACuE,QAAQ,CAAC2U,IAAI,CAAC,EAAE;QACvB;IACF;IAEUC,cAAcC,QAA6B,EAAE;QACrD,IAAI,CAACpb,gBAAgB,CAACqb,GAAG,CAACD;IAC5B;IAEA,MAAME,QAAuB;QAC3B,MAAM,IAAI,CAACtb,gBAAgB,CAACub,MAAM;IACpC;IAEUC,uBAAkC;QAC1C,IAAI,CAACC,iBAAiB,KAAK,IAAI,CAACC,uBAAuB;QACvD,OAAO,IAAI,CAACD,iBAAiB;IAC/B;IAEQC,0BAA0B;QAChC,IAAI,IAAI,CAACjb,WAAW,EAAE;YACpB,MAAM,qBAEL,CAFK,IAAIkb,8BAAc,CACtB,mEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMC,UAAU,IAAIC,oBAAW,CAAC;YAAExB,SAAS3a,QAAQC,KAAK;QAAC;QAEzD,kEAAkE;QAClE,IAAI,CAACwb,aAAa,CAAC,IAAMS,QAAQE,QAAQ;QAEzC,OAAOF,QAAQjL,SAAS;IAC1B;AACF", "ignoreList": [0]}