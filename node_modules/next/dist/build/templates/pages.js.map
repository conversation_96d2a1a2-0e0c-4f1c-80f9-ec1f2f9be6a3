{"version": 3, "sources": ["../../../src/build/templates/pages.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'node:http'\nimport type { ParsedUrlQuery } from 'node:querystring'\nimport { PagesRouteModule } from '../../server/route-modules/pages/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\nimport { BaseServerSpan } from '../../server/lib/trace/constants'\nimport { getTracer, SpanKind, type Span } from '../../server/lib/trace/tracer'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { addRequestMeta, getRequestMeta } from '../../server/request-meta'\nimport { interopDefault } from '../../server/app-render/interop-default'\nimport { getRevalidateReason } from '../../server/instrumentation/utils'\nimport { normalizeDataPath } from '../../shared/lib/page-path/normalize-data-path'\nimport {\n  CachedRouteKind,\n  type CachedPageValue,\n  type CachedRedirectValue,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n} from '../../server/response-cache'\n\nimport { hoist } from './helpers'\n\n// Import the app and document modules.\nimport * as document from 'VAR_MODULE_DOCUMENT'\nimport * as app from 'VAR_MODULE_APP'\n\n// Import the userland code.\nimport * as userland from 'VAR_USERLAND'\nimport {\n  getCacheControlHeader,\n  type CacheControl,\n} from '../../server/lib/cache-control'\nimport { normalizeRepeatedSlashes } from '../../shared/lib/utils'\nimport { getRedirectStatus } from '../../lib/redirect-status'\nimport { CACHE_ONE_YEAR } from '../../lib/constants'\nimport { sendRenderResult } from '../../server/send-payload'\nimport RenderResult from '../../server/render-result'\nimport { toResponseCacheEntry } from '../../server/response-cache/utils'\nimport { NoFallbackError } from '../../shared/lib/no-fallback-error.external'\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport { removeTrailingSlash } from '../../shared/lib/router/utils/remove-trailing-slash'\n\n// Re-export the component (should be the default export).\nexport default hoist(userland, 'default')\n\n// Re-export methods.\nexport const getStaticProps = hoist(userland, 'getStaticProps')\nexport const getStaticPaths = hoist(userland, 'getStaticPaths')\nexport const getServerSideProps = hoist(userland, 'getServerSideProps')\nexport const config = hoist(userland, 'config')\nexport const reportWebVitals = hoist(userland, 'reportWebVitals')\n\n// Re-export legacy methods.\nexport const unstable_getStaticProps = hoist(\n  userland,\n  'unstable_getStaticProps'\n)\nexport const unstable_getStaticPaths = hoist(\n  userland,\n  'unstable_getStaticPaths'\n)\nexport const unstable_getStaticParams = hoist(\n  userland,\n  'unstable_getStaticParams'\n)\nexport const unstable_getServerProps = hoist(\n  userland,\n  'unstable_getServerProps'\n)\nexport const unstable_getServerSideProps = hoist(\n  userland,\n  'unstable_getServerSideProps'\n)\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new PagesRouteModule({\n  definition: {\n    kind: RouteKind.PAGES,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n  },\n  distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n  projectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n  components: {\n    // default export might not exist when optimized for data only\n    App: app.default,\n    Document: document.default,\n  },\n  userland,\n})\n\nexport async function handler(\n  req: IncomingMessage,\n  res: ServerResponse,\n  ctx: {\n    waitUntil: (prom: Promise<void>) => void\n  }\n): Promise<void> {\n  let srcPage = 'VAR_DEFINITION_PAGE'\n\n  // turbopack doesn't normalize `/index` in the page name\n  // so we need to to process dynamic routes properly\n  // TODO: fix turbopack providing differing value from webpack\n  if (process.env.TURBOPACK) {\n    srcPage = srcPage.replace(/\\/index$/, '') || '/'\n  } else if (srcPage === '/index') {\n    // we always normalize /index specifically\n    srcPage = '/'\n  }\n  const multiZoneDraftMode = process.env\n    .__NEXT_MULTI_ZONE_DRAFT_MODE as any as boolean\n\n  const prepareResult = await routeModule.prepare(req, res, {\n    srcPage,\n    multiZoneDraftMode,\n  })\n\n  if (!prepareResult) {\n    res.statusCode = 400\n    res.end('Bad Request')\n    ctx.waitUntil?.(Promise.resolve())\n    return\n  }\n\n  const {\n    buildId,\n    query,\n    params,\n    parsedUrl,\n    originalQuery,\n    originalPathname,\n    buildManifest,\n    nextFontManifest,\n    serverFilesManifest,\n    reactLoadableManifest,\n    prerenderManifest,\n    isDraftMode,\n    isOnDemandRevalidate,\n    revalidateOnlyGenerated,\n    locale,\n    locales,\n    defaultLocale,\n    routerServerContext,\n    nextConfig,\n    resolvedPathname,\n  } = prepareResult\n\n  const isExperimentalCompile =\n    serverFilesManifest?.config?.experimental?.isExperimentalCompile\n\n  const hasServerProps = Boolean(getServerSideProps)\n  const hasStaticProps = Boolean(getStaticProps)\n  const hasStaticPaths = Boolean(getStaticPaths)\n  const hasGetInitialProps = Boolean(\n    (userland.default || userland).getInitialProps\n  )\n  const isAmp = query.amp && config.amp\n  let cacheKey: null | string = null\n  let isIsrFallback = false\n  let isNextDataRequest =\n    prepareResult.isNextDataRequest && (hasStaticProps || hasServerProps)\n\n  const is404Page = srcPage === '/404'\n  const is500Page = srcPage === '/500'\n  const isErrorPage = srcPage === '/_error'\n\n  if (!routeModule.isDev && !isDraftMode && hasStaticProps) {\n    cacheKey = `${locale ? `/${locale}` : ''}${\n      (srcPage === '/' || resolvedPathname === '/') && locale\n        ? ''\n        : resolvedPathname\n    }${isAmp ? '.amp' : ''}`\n\n    if (is404Page || is500Page || isErrorPage) {\n      cacheKey = `${locale ? `/${locale}` : ''}${srcPage}${isAmp ? '.amp' : ''}`\n    }\n\n    // ensure /index and / is normalized to one key\n    cacheKey = cacheKey === '/index' ? '/' : cacheKey\n  }\n\n  if (hasStaticPaths && !isDraftMode) {\n    const decodedPathname = removeTrailingSlash(\n      locale ? addPathPrefix(resolvedPathname, `/${locale}`) : resolvedPathname\n    )\n    const isPrerendered =\n      Boolean(prerenderManifest.routes[decodedPathname]) ||\n      prerenderManifest.notFoundRoutes.includes(decodedPathname)\n\n    const prerenderInfo = prerenderManifest.dynamicRoutes[srcPage]\n\n    if (prerenderInfo) {\n      if (prerenderInfo.fallback === false && !isPrerendered) {\n        throw new NoFallbackError()\n      }\n\n      if (\n        typeof prerenderInfo.fallback === 'string' &&\n        !isPrerendered &&\n        !isNextDataRequest\n      ) {\n        isIsrFallback = true\n      }\n    }\n  }\n\n  // When serving a bot request, we want to serve a blocking render and not\n  // the prerendered page. This ensures that the correct content is served\n  // to the bot in the head.\n  if (\n    (isIsrFallback && isBot(req.headers['user-agent'] || '')) ||\n    getRequestMeta(req, 'minimalMode')\n  ) {\n    isIsrFallback = false\n  }\n\n  const tracer = getTracer()\n  const activeSpan = tracer.getActiveScopeSpan()\n\n  try {\n    const method = req.method || 'GET'\n\n    const resolvedUrl = formatUrl({\n      pathname: nextConfig.trailingSlash\n        ? parsedUrl.pathname\n        : removeTrailingSlash(parsedUrl.pathname || '/'),\n      // make sure to only add query values from original URL\n      query: hasStaticProps ? {} : originalQuery,\n    })\n\n    const publicRuntimeConfig: Record<string, string> =\n      routerServerContext?.publicRuntimeConfig || nextConfig.publicRuntimeConfig\n\n    const handleResponse = async (span?: Span) => {\n      const responseGenerator: ResponseGenerator = async ({\n        previousCacheEntry,\n      }) => {\n        const doRender = async () => {\n          try {\n            return await routeModule\n              .render(req, res, {\n                query:\n                  hasStaticProps && !isExperimentalCompile\n                    ? ({\n                        ...params,\n                        ...(isAmp\n                          ? {\n                              amp: query.amp,\n                            }\n                          : {}),\n                      } as ParsedUrlQuery)\n                    : {\n                        ...query,\n                        ...params,\n                      },\n                params,\n                page: srcPage,\n                renderContext: {\n                  isDraftMode,\n                  isFallback: isIsrFallback,\n                  developmentNotFoundSourcePage: getRequestMeta(\n                    req,\n                    'developmentNotFoundSourcePage'\n                  ),\n                },\n                sharedContext: {\n                  buildId,\n                  customServer:\n                    Boolean(routerServerContext?.isCustomServer) || undefined,\n                  deploymentId: process.env.NEXT_DEPLOYMENT_ID,\n                },\n                renderOpts: {\n                  params,\n                  routeModule,\n                  page: srcPage,\n                  pageConfig: config || {},\n                  Component: interopDefault(userland),\n                  ComponentMod: userland,\n                  getStaticProps,\n                  getStaticPaths,\n                  getServerSideProps,\n                  supportsDynamicResponse: !hasStaticProps,\n                  buildManifest,\n                  nextFontManifest,\n                  reactLoadableManifest,\n\n                  assetPrefix: nextConfig.assetPrefix,\n                  strictNextHead:\n                    nextConfig.experimental.strictNextHead ?? true,\n                  previewProps: prerenderManifest.preview,\n                  images: nextConfig.images as any,\n                  nextConfigOutput: nextConfig.output,\n                  optimizeCss: Boolean(nextConfig.experimental.optimizeCss),\n                  nextScriptWorkers: Boolean(\n                    nextConfig.experimental.nextScriptWorkers\n                  ),\n                  domainLocales: nextConfig.i18n?.domains,\n                  crossOrigin: nextConfig.crossOrigin,\n\n                  multiZoneDraftMode,\n                  basePath: nextConfig.basePath,\n                  canonicalBase: nextConfig.amp.canonicalBase || '',\n                  ampOptimizerConfig: nextConfig.experimental.amp?.optimizer,\n                  disableOptimizedLoading:\n                    nextConfig.experimental.disableOptimizedLoading,\n                  largePageDataBytes:\n                    nextConfig.experimental.largePageDataBytes,\n                  // Only the `publicRuntimeConfig` key is exposed to the client side\n                  // It'll be rendered as part of __NEXT_DATA__ on the client side\n                  runtimeConfig:\n                    Object.keys(publicRuntimeConfig).length > 0\n                      ? publicRuntimeConfig\n                      : undefined,\n\n                  isExperimentalCompile,\n\n                  experimental: {\n                    clientTraceMetadata:\n                      nextConfig.experimental.clientTraceMetadata ||\n                      ([] as any),\n                  },\n\n                  locale,\n                  locales,\n                  defaultLocale,\n                  setIsrStatus: routerServerContext?.setIsrStatus,\n\n                  isNextDataRequest:\n                    isNextDataRequest && (hasServerProps || hasStaticProps),\n\n                  resolvedUrl,\n                  // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n                  // and not the resolved URL to prevent a hydration mismatch on\n                  // asPath\n                  resolvedAsPath:\n                    hasServerProps || hasGetInitialProps\n                      ? formatUrl({\n                          // we use the original URL pathname less the _next/data prefix if\n                          // present\n                          pathname: isNextDataRequest\n                            ? normalizeDataPath(originalPathname)\n                            : originalPathname,\n                          query: originalQuery,\n                        })\n                      : resolvedUrl,\n\n                  isOnDemandRevalidate,\n\n                  ErrorDebug: getRequestMeta(req, 'PagesErrorDebug'),\n                  err: getRequestMeta(req, 'invokeError'),\n                  dev: routeModule.isDev,\n\n                  // needed for experimental.optimizeCss feature\n                  distDir: `${routeModule.projectDir}/${routeModule.distDir}`,\n\n                  ampSkipValidation:\n                    nextConfig.experimental.amp?.skipValidation,\n                  ampValidator: getRequestMeta(req, 'ampValidator'),\n                },\n              })\n              .then((renderResult): ResponseCacheEntry => {\n                const { metadata } = renderResult\n\n                let cacheControl: CacheControl | undefined =\n                  metadata.cacheControl\n\n                if ('isNotFound' in metadata && metadata.isNotFound) {\n                  return {\n                    value: null,\n                    cacheControl,\n                  } satisfies ResponseCacheEntry\n                }\n\n                // Handle `isRedirect`.\n                if (metadata.isRedirect) {\n                  return {\n                    value: {\n                      kind: CachedRouteKind.REDIRECT,\n                      props: metadata.pageData ?? metadata.flightData,\n                    } satisfies CachedRedirectValue,\n                    cacheControl,\n                  } satisfies ResponseCacheEntry\n                }\n\n                return {\n                  value: {\n                    kind: CachedRouteKind.PAGES,\n                    html: renderResult,\n                    pageData: renderResult.metadata.pageData,\n                    headers: renderResult.metadata.headers,\n                    status: renderResult.metadata.statusCode,\n                  },\n                  cacheControl,\n                }\n              })\n              .finally(() => {\n                if (!span) return\n\n                span.setAttributes({\n                  'http.status_code': res.statusCode,\n                  'next.rsc': false,\n                })\n\n                const rootSpanAttributes = tracer.getRootSpanAttributes()\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                  return\n                }\n\n                if (\n                  rootSpanAttributes.get('next.span_type') !==\n                  BaseServerSpan.handleRequest\n                ) {\n                  console.warn(\n                    `Unexpected root span type '${rootSpanAttributes.get(\n                      'next.span_type'\n                    )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n                  )\n                  return\n                }\n\n                const route = rootSpanAttributes.get('next.route')\n                if (route) {\n                  const name = `${method} ${route}`\n\n                  span.setAttributes({\n                    'next.route': route,\n                    'http.route': route,\n                    'next.span_name': name,\n                  })\n                  span.updateName(name)\n                } else {\n                  span.updateName(`${method} ${req.url}`)\n                }\n              })\n          } catch (err: unknown) {\n            // if this is a background revalidate we need to report\n            // the request error here as it won't be bubbled\n            if (previousCacheEntry?.isStale) {\n              await routeModule.onRequestError(\n                req,\n                err,\n                {\n                  routerKind: 'Pages Router',\n                  routePath: srcPage,\n                  routeType: 'render',\n                  revalidateReason: getRevalidateReason({\n                    isRevalidate: hasStaticProps,\n                    isOnDemandRevalidate,\n                  }),\n                },\n                routerServerContext\n              )\n            }\n            throw err\n          }\n        }\n\n        // if we've already generated this page we no longer\n        // serve the fallback\n        if (previousCacheEntry) {\n          isIsrFallback = false\n        }\n\n        if (isIsrFallback) {\n          const fallbackResponse = await routeModule.getResponseCache(req).get(\n            routeModule.isDev\n              ? null\n              : locale\n                ? `/${locale}${srcPage}`\n                : srcPage,\n            async ({\n              previousCacheEntry: previousFallbackCacheEntry = null,\n            }) => {\n              if (!routeModule.isDev) {\n                return toResponseCacheEntry(previousFallbackCacheEntry)\n              }\n              return doRender()\n            },\n            {\n              routeKind: RouteKind.PAGES,\n              isFallback: true,\n              isRoutePPREnabled: false,\n              isOnDemandRevalidate: false,\n              incrementalCache: await routeModule.getIncrementalCache(\n                req,\n                nextConfig,\n                prerenderManifest\n              ),\n              waitUntil: ctx.waitUntil,\n            }\n          )\n          if (fallbackResponse) {\n            // Remove the cache control from the response to prevent it from being\n            // used in the surrounding cache.\n            delete fallbackResponse.cacheControl\n            fallbackResponse.isMiss = true\n            return fallbackResponse\n          }\n        }\n\n        if (\n          !getRequestMeta(req, 'minimalMode') &&\n          isOnDemandRevalidate &&\n          revalidateOnlyGenerated &&\n          !previousCacheEntry\n        ) {\n          res.statusCode = 404\n          // on-demand revalidate always sets this header\n          res.setHeader('x-nextjs-cache', 'REVALIDATED')\n          res.end('This page could not be found')\n          return null\n        }\n\n        if (\n          isIsrFallback &&\n          previousCacheEntry?.value?.kind === CachedRouteKind.PAGES\n        ) {\n          return {\n            value: {\n              kind: CachedRouteKind.PAGES,\n              html: new RenderResult(\n                Buffer.from(previousCacheEntry.value.html),\n                {\n                  contentType: 'text/html;utf-8',\n                  metadata: {\n                    statusCode: previousCacheEntry.value.status,\n                    headers: previousCacheEntry.value.headers,\n                  },\n                }\n              ),\n              pageData: {},\n              status: previousCacheEntry.value.status,\n              headers: previousCacheEntry.value.headers,\n            } satisfies CachedPageValue,\n            cacheControl: { revalidate: 0, expire: undefined },\n          } satisfies ResponseCacheEntry\n        }\n        return doRender()\n      }\n\n      const result = await routeModule.handleResponse({\n        cacheKey,\n        req,\n        nextConfig,\n        routeKind: RouteKind.PAGES,\n        isOnDemandRevalidate,\n        revalidateOnlyGenerated,\n        waitUntil: ctx.waitUntil,\n        responseGenerator: responseGenerator,\n        prerenderManifest,\n      })\n\n      // if we got a cache hit this wasn't an ISR fallback\n      // but it wasn't generated during build so isn't in the\n      // prerender-manifest\n      if (isIsrFallback && !result?.isMiss) {\n        isIsrFallback = false\n      }\n\n      // response is finished is no cache entry\n      if (!result) {\n        return\n      }\n\n      if (hasStaticProps && !getRequestMeta(req, 'minimalMode')) {\n        res.setHeader(\n          'x-nextjs-cache',\n          isOnDemandRevalidate\n            ? 'REVALIDATED'\n            : result.isMiss\n              ? 'MISS'\n              : result.isStale\n                ? 'STALE'\n                : 'HIT'\n        )\n      }\n\n      let cacheControl: CacheControl | undefined\n\n      if (!hasStaticProps || isIsrFallback) {\n        if (!res.getHeader('Cache-Control')) {\n          cacheControl = { revalidate: 0, expire: undefined }\n        }\n      } else if (is404Page) {\n        const notFoundRevalidate = getRequestMeta(req, 'notFoundRevalidate')\n\n        cacheControl = {\n          revalidate:\n            typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate,\n          expire: undefined,\n        }\n      } else if (is500Page) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      } else if (result.cacheControl) {\n        // If the cache entry has a cache control with a revalidate value that's\n        // a number, use it.\n        if (typeof result.cacheControl.revalidate === 'number') {\n          if (result.cacheControl.revalidate < 1) {\n            throw new Error(\n              `Invalid revalidate configuration provided: ${result.cacheControl.revalidate} < 1`\n            )\n          }\n          cacheControl = {\n            revalidate: result.cacheControl.revalidate,\n            expire: result.cacheControl?.expire ?? nextConfig.expireTime,\n          }\n        } else {\n          // revalidate: false\n          cacheControl = {\n            revalidate: CACHE_ONE_YEAR,\n            expire: undefined,\n          }\n        }\n      }\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (cacheControl && !res.getHeader('Cache-Control')) {\n        res.setHeader('Cache-Control', getCacheControlHeader(cacheControl))\n      }\n\n      // notFound: true case\n      if (!result.value) {\n        // add revalidate metadata before rendering 404 page\n        // so that we can use this as source of truth for the\n        // cache-control header instead of what the 404 page returns\n        // for the revalidate value\n        addRequestMeta(\n          req,\n          'notFoundRevalidate',\n          result.cacheControl?.revalidate\n        )\n\n        res.statusCode = 404\n\n        if (isNextDataRequest) {\n          res.end('{\"notFound\":true}')\n          return\n        }\n        // TODO: should route-module itself handle rendering the 404\n        if (routerServerContext?.render404) {\n          await routerServerContext.render404(req, res, parsedUrl, false)\n        } else {\n          res.end('This page could not be found')\n        }\n        return\n      }\n\n      if (result.value.kind === CachedRouteKind.REDIRECT) {\n        if (isNextDataRequest) {\n          res.setHeader('content-type', 'application/json')\n          res.end(JSON.stringify(result.value.props))\n          return\n        } else {\n          const handleRedirect = (pageData: any) => {\n            const redirect = {\n              destination: pageData.pageProps.__N_REDIRECT,\n              statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n              basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH,\n            }\n            const statusCode = getRedirectStatus(redirect)\n            const { basePath } = nextConfig\n\n            if (\n              basePath &&\n              redirect.basePath !== false &&\n              redirect.destination.startsWith('/')\n            ) {\n              redirect.destination = `${basePath}${redirect.destination}`\n            }\n\n            if (redirect.destination.startsWith('/')) {\n              redirect.destination = normalizeRepeatedSlashes(\n                redirect.destination\n              )\n            }\n\n            res.statusCode = statusCode\n            res.setHeader('Location', redirect.destination)\n            if (statusCode === RedirectStatusCode.PermanentRedirect) {\n              res.setHeader('Refresh', `0;url=${redirect.destination}`)\n            }\n            res.end(redirect.destination)\n          }\n          await handleRedirect(result.value.props)\n          return null\n        }\n      }\n\n      if (result.value.kind !== CachedRouteKind.PAGES) {\n        throw new Error(\n          `Invariant: received non-pages cache entry in pages handler`\n        )\n      }\n\n      // In dev, we should not cache pages for any reason.\n      if (routeModule.isDev) {\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n      }\n\n      // Draft mode should never be cached\n      if (isDraftMode) {\n        res.setHeader(\n          'Cache-Control',\n          'private, no-cache, no-store, max-age=0, must-revalidate'\n        )\n      }\n\n      // when invoking _error before pages/500 we don't actually\n      // send the _error response\n      if (\n        getRequestMeta(req, 'customErrorRender') ||\n        (isErrorPage &&\n          getRequestMeta(req, 'minimalMode') &&\n          res.statusCode === 500)\n      ) {\n        return null\n      }\n\n      await sendRenderResult({\n        req,\n        res,\n        // If we are rendering the error page it's not a data request\n        // anymore\n        result:\n          isNextDataRequest && !isErrorPage && !is500Page\n            ? new RenderResult(\n                Buffer.from(JSON.stringify(result.value.pageData)),\n                {\n                  contentType: 'application/json',\n                  metadata: result.value.html.metadata,\n                }\n              )\n            : result.value.html,\n        generateEtags: nextConfig.generateEtags,\n        poweredByHeader: nextConfig.poweredByHeader,\n        cacheControl: routeModule.isDev ? undefined : cacheControl,\n        type: isNextDataRequest ? 'json' : 'html',\n      })\n    }\n\n    // TODO: activeSpan code path is for when wrapped by\n    // next-server can be removed when this is no longer used\n    if (activeSpan) {\n      await handleResponse()\n    } else {\n      await tracer.withPropagatedContext(req.headers, () =>\n        tracer.trace(\n          BaseServerSpan.handleRequest,\n          {\n            spanName: `${method} ${req.url}`,\n            kind: SpanKind.SERVER,\n            attributes: {\n              'http.method': method,\n              'http.target': req.url,\n            },\n          },\n          handleResponse\n        )\n      )\n    }\n  } catch (err) {\n    await routeModule.onRequestError(\n      req,\n      err,\n      {\n        routerKind: 'Pages Router',\n        routePath: srcPage,\n        routeType: 'render',\n        revalidateReason: getRevalidateReason({\n          isRevalidate: hasStaticProps,\n          isOnDemandRevalidate,\n        }),\n      },\n      routerServerContext\n    )\n\n    // rethrow so that we can handle serving error page\n    throw err\n  }\n}\n"], "names": ["config", "getServerSideProps", "getStaticPaths", "getStaticProps", "handler", "reportWebVitals", "routeModule", "unstable_getServerProps", "unstable_getServerSideProps", "unstable_getStaticParams", "unstable_getStaticPaths", "unstable_getStaticProps", "hoist", "userland", "PagesRouteModule", "definition", "kind", "RouteKind", "PAGES", "page", "pathname", "bundlePath", "filename", "distDir", "process", "env", "__NEXT_RELATIVE_DIST_DIR", "projectDir", "__NEXT_RELATIVE_PROJECT_DIR", "components", "App", "app", "default", "Document", "document", "req", "res", "ctx", "serverFilesManifest", "srcPage", "TURBOPACK", "replace", "multiZoneDraftMode", "__NEXT_MULTI_ZONE_DRAFT_MODE", "prepareResult", "prepare", "statusCode", "end", "waitUntil", "Promise", "resolve", "buildId", "query", "params", "parsedUrl", "originalQuery", "originalPathname", "buildManifest", "nextFontManifest", "reactLoadableManifest", "prerenderManifest", "isDraftMode", "isOnDemandRevalidate", "revalidateOnlyGenerated", "locale", "locales", "defaultLocale", "routerServerContext", "nextConfig", "resolvedPathname", "isExperimentalCompile", "experimental", "hasServerProps", "Boolean", "hasStaticProps", "hasStaticPaths", "hasGetInitialProps", "getInitialProps", "isAmp", "amp", "cache<PERSON>ey", "isIsrFallback", "isNextDataRequest", "is404Page", "is500Page", "isErrorPage", "isDev", "decodedPathname", "removeTrailingSlash", "addPathPrefix", "isP<PERSON>endered", "routes", "notFoundRoutes", "includes", "prerenderInfo", "dynamicRoutes", "fallback", "NoFallbackError", "isBot", "headers", "getRequestMeta", "tracer", "getTracer", "activeSpan", "getActiveScopeSpan", "method", "resolvedUrl", "formatUrl", "trailingSlash", "publicRuntimeConfig", "handleResponse", "span", "responseGenerator", "previousCacheEntry", "doR<PERSON>", "render", "renderContext", "<PERSON><PERSON><PERSON><PERSON>", "developmentNotFoundSourcePage", "sharedContext", "customServer", "isCustomServer", "undefined", "deploymentId", "NEXT_DEPLOYMENT_ID", "renderOpts", "pageConfig", "Component", "interopDefault", "ComponentMod", "supportsDynamicResponse", "assetPrefix", "strictNextHead", "previewProps", "preview", "images", "nextConfigOutput", "output", "optimizeCss", "nextScriptWorkers", "domainLocales", "i18n", "domains", "crossOrigin", "basePath", "canonicalBase", "ampOptimizerConfig", "optimizer", "disableOptimizedLoading", "largePageDataBytes", "runtimeConfig", "Object", "keys", "length", "clientTraceMetadata", "setIsrStatus", "resolvedAsPath", "normalizeDataPath", "ErrorDebug", "err", "dev", "ampSkipValidation", "skipValidation", "ampValidator", "then", "renderResult", "metadata", "cacheControl", "isNotFound", "value", "isRedirect", "CachedRouteKind", "REDIRECT", "props", "pageData", "flightData", "html", "status", "finally", "setAttributes", "rootSpanAttributes", "getRootSpanAttributes", "get", "BaseServerSpan", "handleRequest", "console", "warn", "route", "name", "updateName", "url", "isStale", "onRequestError", "routerKind", "routePath", "routeType", "revalidateReason", "getRevalidateReason", "isRevalidate", "fallbackResponse", "getResponseCache", "previousFallbackCacheEntry", "toResponseCacheEntry", "routeKind", "isRoutePPREnabled", "incrementalCache", "getIncrementalCache", "isMiss", "<PERSON><PERSON><PERSON><PERSON>", "RenderResult", "<PERSON><PERSON><PERSON>", "from", "contentType", "revalidate", "expire", "result", "<PERSON><PERSON><PERSON><PERSON>", "notFoundRevalidate", "Error", "expireTime", "CACHE_ONE_YEAR", "getCacheControlHeader", "addRequestMeta", "render404", "JSON", "stringify", "handleRedirect", "redirect", "destination", "pageProps", "__N_REDIRECT", "__N_REDIRECT_STATUS", "__N_REDIRECT_BASE_PATH", "getRedirectStatus", "startsWith", "normalizeRepeatedSlashes", "RedirectStatusCode", "PermanentRedirect", "sendRenderResult", "generateEtags", "poweredByHeader", "type", "withPropagatedContext", "trace", "spanName", "SpanKind", "SERVER", "attributes"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;IAkDaA,MAAM;eAANA;;IAPb,0DAA0D;IAC1D,OAAyC;eAAzC;;IAKaC,kBAAkB;eAAlBA;;IADAC,cAAc;eAAdA;;IADAC,cAAc;eAAdA;;IAgDSC,OAAO;eAAPA;;IA5CTC,eAAe;eAAfA;;IAyBAC,WAAW;eAAXA;;IAVAC,uBAAuB;eAAvBA;;IAIAC,2BAA2B;eAA3BA;;IARAC,wBAAwB;eAAxBA;;IAJAC,uBAAuB;eAAvBA;;IAJAC,uBAAuB;eAAvBA;;;gCApDoB;2BACP;2BACK;wBACgB;2BACrB;6BACqB;gCAChB;uBACK;mCACF;+BAO3B;yBAEe;6EAGI;wEACL;sEAGK;8BAInB;wBACkC;gCACP;4BACH;6BACE;qEACR;wBACY;yCACL;oCACG;uBACb;+BACQ;qCACM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAGpC,WAAeC,IAAAA,cAAK,EAACC,eAAU;AAGxB,MAAMV,iBAAiBS,IAAAA,cAAK,EAACC,eAAU;AACvC,MAAMX,iBAAiBU,IAAAA,cAAK,EAACC,eAAU;AACvC,MAAMZ,qBAAqBW,IAAAA,cAAK,EAACC,eAAU;AAC3C,MAAMb,SAASY,IAAAA,cAAK,EAACC,eAAU;AAC/B,MAAMR,kBAAkBO,IAAAA,cAAK,EAACC,eAAU;AAGxC,MAAMF,0BAA0BC,IAAAA,cAAK,EAC1CC,eACA;AAEK,MAAMH,0BAA0BE,IAAAA,cAAK,EAC1CC,eACA;AAEK,MAAMJ,2BAA2BG,IAAAA,cAAK,EAC3CC,eACA;AAEK,MAAMN,0BAA0BK,IAAAA,cAAK,EAC1CC,eACA;AAEK,MAAML,8BAA8BI,IAAAA,cAAK,EAC9CC,eACA;AAIK,MAAMP,cAAc,IAAIQ,gCAAgB,CAAC;IAC9CC,YAAY;QACVC,MAAMC,oBAAS,CAACC,KAAK;QACrBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;IACAC,SAASC,QAAQC,GAAG,CAACC,wBAAwB,IAAI;IACjDC,YAAYH,QAAQC,GAAG,CAACG,2BAA2B,IAAI;IACvDC,YAAY;QACV,8DAA8D;QAC9DC,KAAKC,gBAAIC,OAAO;QAChBC,UAAUC,qBAASF,OAAO;IAC5B;IACAnB,UAAAA;AACF;AAEO,eAAeT,QACpB+B,GAAoB,EACpBC,GAAmB,EACnBC,GAEC;QAoDCC,0CAAAA;IAlDF,IAAIC,UAAU;IAEd,wDAAwD;IACxD,mDAAmD;IACnD,6DAA6D;IAC7D,IAAIf,QAAQC,GAAG,CAACe,SAAS,EAAE;QACzBD,UAAUA,QAAQE,OAAO,CAAC,YAAY,OAAO;IAC/C,OAAO,IAAIF,YAAY,UAAU;QAC/B,0CAA0C;QAC1CA,UAAU;IACZ;IACA,MAAMG,qBAAqBlB,QAAQC,GAAG,CACnCkB,4BAA4B;IAE/B,MAAMC,gBAAgB,MAAMtC,YAAYuC,OAAO,CAACV,KAAKC,KAAK;QACxDG;QACAG;IACF;IAEA,IAAI,CAACE,eAAe;QAClBR,IAAIU,UAAU,GAAG;QACjBV,IAAIW,GAAG,CAAC;QACRV,IAAIW,SAAS,oBAAbX,IAAIW,SAAS,MAAbX,KAAgBY,QAAQC,OAAO;QAC/B;IACF;IAEA,MAAM,EACJC,OAAO,EACPC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,gBAAgB,EAChBpB,mBAAmB,EACnBqB,qBAAqB,EACrBC,iBAAiB,EACjBC,WAAW,EACXC,oBAAoB,EACpBC,uBAAuB,EACvBC,MAAM,EACNC,OAAO,EACPC,aAAa,EACbC,mBAAmB,EACnBC,UAAU,EACVC,gBAAgB,EACjB,GAAGzB;IAEJ,MAAM0B,wBACJhC,wCAAAA,8BAAAA,oBAAqBtC,MAAM,sBAA3BsC,2CAAAA,4BAA6BiC,YAAY,qBAAzCjC,yCAA2CgC,qBAAqB;IAElE,MAAME,iBAAiBC,QAAQxE;IAC/B,MAAMyE,iBAAiBD,QAAQtE;IAC/B,MAAMwE,iBAAiBF,QAAQvE;IAC/B,MAAM0E,qBAAqBH,QACzB,AAAC5D,CAAAA,cAASmB,OAAO,IAAInB,aAAO,EAAGgE,eAAe;IAEhD,MAAMC,QAAQ1B,MAAM2B,GAAG,IAAI/E,OAAO+E,GAAG;IACrC,IAAIC,WAA0B;IAC9B,IAAIC,gBAAgB;IACpB,IAAIC,oBACFtC,cAAcsC,iBAAiB,IAAKR,CAAAA,kBAAkBF,cAAa;IAErE,MAAMW,YAAY5C,YAAY;IAC9B,MAAM6C,YAAY7C,YAAY;IAC9B,MAAM8C,cAAc9C,YAAY;IAEhC,IAAI,CAACjC,YAAYgF,KAAK,IAAI,CAACzB,eAAea,gBAAgB;QACxDM,WAAW,GAAGhB,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KACpC,AAACzB,CAAAA,YAAY,OAAO8B,qBAAqB,GAAE,KAAML,SAC7C,KACAK,mBACHS,QAAQ,SAAS,IAAI;QAExB,IAAIK,aAAaC,aAAaC,aAAa;YACzCL,WAAW,GAAGhB,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAKzB,UAAUuC,QAAQ,SAAS,IAAI;QAC5E;QAEA,+CAA+C;QAC/CE,WAAWA,aAAa,WAAW,MAAMA;IAC3C;IAEA,IAAIL,kBAAkB,CAACd,aAAa;QAClC,MAAM0B,kBAAkBC,IAAAA,wCAAmB,EACzCxB,SAASyB,IAAAA,4BAAa,EAACpB,kBAAkB,CAAC,CAAC,EAAEL,QAAQ,IAAIK;QAE3D,MAAMqB,gBACJjB,QAAQb,kBAAkB+B,MAAM,CAACJ,gBAAgB,KACjD3B,kBAAkBgC,cAAc,CAACC,QAAQ,CAACN;QAE5C,MAAMO,gBAAgBlC,kBAAkBmC,aAAa,CAACxD,QAAQ;QAE9D,IAAIuD,eAAe;YACjB,IAAIA,cAAcE,QAAQ,KAAK,SAAS,CAACN,eAAe;gBACtD,MAAM,IAAIO,wCAAe;YAC3B;YAEA,IACE,OAAOH,cAAcE,QAAQ,KAAK,YAClC,CAACN,iBACD,CAACR,mBACD;gBACAD,gBAAgB;YAClB;QACF;IACF;IAEA,yEAAyE;IACzE,wEAAwE;IACxE,0BAA0B;IAC1B,IACE,AAACA,iBAAiBiB,IAAAA,YAAK,EAAC/D,IAAIgE,OAAO,CAAC,aAAa,IAAI,OACrDC,IAAAA,2BAAc,EAACjE,KAAK,gBACpB;QACA8C,gBAAgB;IAClB;IAEA,MAAMoB,SAASC,IAAAA,iBAAS;IACxB,MAAMC,aAAaF,OAAOG,kBAAkB;IAE5C,IAAI;QACF,MAAMC,SAAStE,IAAIsE,MAAM,IAAI;QAE7B,MAAMC,cAAcC,IAAAA,oBAAS,EAAC;YAC5BvF,UAAUgD,WAAWwC,aAAa,GAC9BtD,UAAUlC,QAAQ,GAClBoE,IAAAA,wCAAmB,EAAClC,UAAUlC,QAAQ,IAAI;YAC9C,uDAAuD;YACvDgC,OAAOsB,iBAAiB,CAAC,IAAInB;QAC/B;QAEA,MAAMsD,sBACJ1C,CAAAA,uCAAAA,oBAAqB0C,mBAAmB,KAAIzC,WAAWyC,mBAAmB;QAE5E,MAAMC,iBAAiB,OAAOC;YAC5B,MAAMC,oBAAuC,OAAO,EAClDC,kBAAkB,EACnB;oBAwRGA;gBAvRF,MAAMC,WAAW;oBACf,IAAI;4BA0DmB9C,kBAMKA,8BAsDlBA;wBArHR,OAAO,MAAM9D,YACV6G,MAAM,CAAChF,KAAKC,KAAK;4BAChBgB,OACEsB,kBAAkB,CAACJ,wBACd;gCACC,GAAGjB,MAAM;gCACT,GAAIyB,QACA;oCACEC,KAAK3B,MAAM2B,GAAG;gCAChB,IACA,CAAC,CAAC;4BACR,IACA;gCACE,GAAG3B,KAAK;gCACR,GAAGC,MAAM;4BACX;4BACNA;4BACAlC,MAAMoB;4BACN6E,eAAe;gCACbvD;gCACAwD,YAAYpC;gCACZqC,+BAA+BlB,IAAAA,2BAAc,EAC3CjE,KACA;4BAEJ;4BACAoF,eAAe;gCACbpE;gCACAqE,cACE/C,QAAQN,uCAAAA,oBAAqBsD,cAAc,KAAKC;gCAClDC,cAAcnG,QAAQC,GAAG,CAACmG,kBAAkB;4BAC9C;4BACAC,YAAY;gCACVxE;gCACA/C;gCACAa,MAAMoB;gCACNuF,YAAY9H,UAAU,CAAC;gCACvB+H,WAAWC,IAAAA,8BAAc,EAACnH;gCAC1BoH,cAAcpH;gCACdV;gCACAD;gCACAD;gCACAiI,yBAAyB,CAACxD;gCAC1BjB;gCACAC;gCACAC;gCAEAwE,aAAa/D,WAAW+D,WAAW;gCACnCC,gBACEhE,WAAWG,YAAY,CAAC6D,cAAc,IAAI;gCAC5CC,cAAczE,kBAAkB0E,OAAO;gCACvCC,QAAQnE,WAAWmE,MAAM;gCACzBC,kBAAkBpE,WAAWqE,MAAM;gCACnCC,aAAajE,QAAQL,WAAWG,YAAY,CAACmE,WAAW;gCACxDC,mBAAmBlE,QACjBL,WAAWG,YAAY,CAACoE,iBAAiB;gCAE3CC,aAAa,GAAExE,mBAAAA,WAAWyE,IAAI,qBAAfzE,iBAAiB0E,OAAO;gCACvCC,aAAa3E,WAAW2E,WAAW;gCAEnCrG;gCACAsG,UAAU5E,WAAW4E,QAAQ;gCAC7BC,eAAe7E,WAAWW,GAAG,CAACkE,aAAa,IAAI;gCAC/CC,kBAAkB,GAAE9E,+BAAAA,WAAWG,YAAY,CAACQ,GAAG,qBAA3BX,6BAA6B+E,SAAS;gCAC1DC,yBACEhF,WAAWG,YAAY,CAAC6E,uBAAuB;gCACjDC,oBACEjF,WAAWG,YAAY,CAAC8E,kBAAkB;gCAC5C,mEAAmE;gCACnE,gEAAgE;gCAChEC,eACEC,OAAOC,IAAI,CAAC3C,qBAAqB4C,MAAM,GAAG,IACtC5C,sBACAa;gCAENpD;gCAEAC,cAAc;oCACZmF,qBACEtF,WAAWG,YAAY,CAACmF,mBAAmB,IAC1C,EAAE;gCACP;gCAEA1F;gCACAC;gCACAC;gCACAyF,YAAY,EAAExF,uCAAAA,oBAAqBwF,YAAY;gCAE/CzE,mBACEA,qBAAsBV,CAAAA,kBAAkBE,cAAa;gCAEvDgC;gCACA,uFAAuF;gCACvF,8DAA8D;gCAC9D,SAAS;gCACTkD,gBACEpF,kBAAkBI,qBACd+B,IAAAA,oBAAS,EAAC;oCACR,iEAAiE;oCACjE,UAAU;oCACVvF,UAAU8D,oBACN2E,IAAAA,oCAAiB,EAACrG,oBAClBA;oCACJJ,OAAOG;gCACT,KACAmD;gCAEN5C;gCAEAgG,YAAY1D,IAAAA,2BAAc,EAACjE,KAAK;gCAChC4H,KAAK3D,IAAAA,2BAAc,EAACjE,KAAK;gCACzB6H,KAAK1J,YAAYgF,KAAK;gCAEtB,8CAA8C;gCAC9C/D,SAAS,GAAGjB,YAAYqB,UAAU,CAAC,CAAC,EAAErB,YAAYiB,OAAO,EAAE;gCAE3D0I,iBAAiB,GACf7F,gCAAAA,WAAWG,YAAY,CAACQ,GAAG,qBAA3BX,8BAA6B8F,cAAc;gCAC7CC,cAAc/D,IAAAA,2BAAc,EAACjE,KAAK;4BACpC;wBACF,GACCiI,IAAI,CAAC,CAACC;4BACL,MAAM,EAAEC,QAAQ,EAAE,GAAGD;4BAErB,IAAIE,eACFD,SAASC,YAAY;4BAEvB,IAAI,gBAAgBD,YAAYA,SAASE,UAAU,EAAE;gCACnD,OAAO;oCACLC,OAAO;oCACPF;gCACF;4BACF;4BAEA,uBAAuB;4BACvB,IAAID,SAASI,UAAU,EAAE;gCACvB,OAAO;oCACLD,OAAO;wCACLzJ,MAAM2J,8BAAe,CAACC,QAAQ;wCAC9BC,OAAOP,SAASQ,QAAQ,IAAIR,SAASS,UAAU;oCACjD;oCACAR;gCACF;4BACF;4BAEA,OAAO;gCACLE,OAAO;oCACLzJ,MAAM2J,8BAAe,CAACzJ,KAAK;oCAC3B8J,MAAMX;oCACNS,UAAUT,aAAaC,QAAQ,CAACQ,QAAQ;oCACxC3E,SAASkE,aAAaC,QAAQ,CAACnE,OAAO;oCACtC8E,QAAQZ,aAAaC,QAAQ,CAACxH,UAAU;gCAC1C;gCACAyH;4BACF;wBACF,GACCW,OAAO,CAAC;4BACP,IAAI,CAACnE,MAAM;4BAEXA,KAAKoE,aAAa,CAAC;gCACjB,oBAAoB/I,IAAIU,UAAU;gCAClC,YAAY;4BACd;4BAEA,MAAMsI,qBAAqB/E,OAAOgF,qBAAqB;4BACvD,iEAAiE;4BACjE,IAAI,CAACD,oBAAoB;gCACvB;4BACF;4BAEA,IACEA,mBAAmBE,GAAG,CAAC,sBACvBC,yBAAc,CAACC,aAAa,EAC5B;gCACAC,QAAQC,IAAI,CACV,CAAC,2BAA2B,EAAEN,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;gCAE1E;4BACF;4BAEA,MAAMK,QAAQP,mBAAmBE,GAAG,CAAC;4BACrC,IAAIK,OAAO;gCACT,MAAMC,OAAO,GAAGnF,OAAO,CAAC,EAAEkF,OAAO;gCAEjC5E,KAAKoE,aAAa,CAAC;oCACjB,cAAcQ;oCACd,cAAcA;oCACd,kBAAkBC;gCACpB;gCACA7E,KAAK8E,UAAU,CAACD;4BAClB,OAAO;gCACL7E,KAAK8E,UAAU,CAAC,GAAGpF,OAAO,CAAC,EAAEtE,IAAI2J,GAAG,EAAE;4BACxC;wBACF;oBACJ,EAAE,OAAO/B,KAAc;wBACrB,uDAAuD;wBACvD,gDAAgD;wBAChD,IAAI9C,sCAAAA,mBAAoB8E,OAAO,EAAE;4BAC/B,MAAMzL,YAAY0L,cAAc,CAC9B7J,KACA4H,KACA;gCACEkC,YAAY;gCACZC,WAAW3J;gCACX4J,WAAW;gCACXC,kBAAkBC,IAAAA,0BAAmB,EAAC;oCACpCC,cAAc5H;oCACdZ;gCACF;4BACF,GACAK;wBAEJ;wBACA,MAAM4F;oBACR;gBACF;gBAEA,oDAAoD;gBACpD,qBAAqB;gBACrB,IAAI9C,oBAAoB;oBACtBhC,gBAAgB;gBAClB;gBAEA,IAAIA,eAAe;oBACjB,MAAMsH,mBAAmB,MAAMjM,YAAYkM,gBAAgB,CAACrK,KAAKmJ,GAAG,CAClEhL,YAAYgF,KAAK,GACb,OACAtB,SACE,CAAC,CAAC,EAAEA,SAASzB,SAAS,GACtBA,SACN,OAAO,EACL0E,oBAAoBwF,6BAA6B,IAAI,EACtD;wBACC,IAAI,CAACnM,YAAYgF,KAAK,EAAE;4BACtB,OAAOoH,IAAAA,4BAAoB,EAACD;wBAC9B;wBACA,OAAOvF;oBACT,GACA;wBACEyF,WAAW1L,oBAAS,CAACC,KAAK;wBAC1BmG,YAAY;wBACZuF,mBAAmB;wBACnB9I,sBAAsB;wBACtB+I,kBAAkB,MAAMvM,YAAYwM,mBAAmB,CACrD3K,KACAiC,YACAR;wBAEFZ,WAAWX,IAAIW,SAAS;oBAC1B;oBAEF,IAAIuJ,kBAAkB;wBACpB,sEAAsE;wBACtE,iCAAiC;wBACjC,OAAOA,iBAAiBhC,YAAY;wBACpCgC,iBAAiBQ,MAAM,GAAG;wBAC1B,OAAOR;oBACT;gBACF;gBAEA,IACE,CAACnG,IAAAA,2BAAc,EAACjE,KAAK,kBACrB2B,wBACAC,2BACA,CAACkD,oBACD;oBACA7E,IAAIU,UAAU,GAAG;oBACjB,+CAA+C;oBAC/CV,IAAI4K,SAAS,CAAC,kBAAkB;oBAChC5K,IAAIW,GAAG,CAAC;oBACR,OAAO;gBACT;gBAEA,IACEkC,iBACAgC,CAAAA,uCAAAA,4BAAAA,mBAAoBwD,KAAK,qBAAzBxD,0BAA2BjG,IAAI,MAAK2J,8BAAe,CAACzJ,KAAK,EACzD;oBACA,OAAO;wBACLuJ,OAAO;4BACLzJ,MAAM2J,8BAAe,CAACzJ,KAAK;4BAC3B8J,MAAM,IAAIiC,qBAAY,CACpBC,OAAOC,IAAI,CAAClG,mBAAmBwD,KAAK,CAACO,IAAI,GACzC;gCACEoC,aAAa;gCACb9C,UAAU;oCACRxH,YAAYmE,mBAAmBwD,KAAK,CAACQ,MAAM;oCAC3C9E,SAASc,mBAAmBwD,KAAK,CAACtE,OAAO;gCAC3C;4BACF;4BAEF2E,UAAU,CAAC;4BACXG,QAAQhE,mBAAmBwD,KAAK,CAACQ,MAAM;4BACvC9E,SAASc,mBAAmBwD,KAAK,CAACtE,OAAO;wBAC3C;wBACAoE,cAAc;4BAAE8C,YAAY;4BAAGC,QAAQ5F;wBAAU;oBACnD;gBACF;gBACA,OAAOR;YACT;YAEA,MAAMqG,SAAS,MAAMjN,YAAYwG,cAAc,CAAC;gBAC9C9B;gBACA7C;gBACAiC;gBACAuI,WAAW1L,oBAAS,CAACC,KAAK;gBAC1B4C;gBACAC;gBACAf,WAAWX,IAAIW,SAAS;gBACxBgE,mBAAmBA;gBACnBpD;YACF;YAEA,oDAAoD;YACpD,uDAAuD;YACvD,qBAAqB;YACrB,IAAIqB,iBAAiB,EAACsI,0BAAAA,OAAQR,MAAM,GAAE;gBACpC9H,gBAAgB;YAClB;YAEA,yCAAyC;YACzC,IAAI,CAACsI,QAAQ;gBACX;YACF;YAEA,IAAI7I,kBAAkB,CAAC0B,IAAAA,2BAAc,EAACjE,KAAK,gBAAgB;gBACzDC,IAAI4K,SAAS,CACX,kBACAlJ,uBACI,gBACAyJ,OAAOR,MAAM,GACX,SACAQ,OAAOxB,OAAO,GACZ,UACA;YAEZ;YAEA,IAAIxB;YAEJ,IAAI,CAAC7F,kBAAkBO,eAAe;gBACpC,IAAI,CAAC7C,IAAIoL,SAAS,CAAC,kBAAkB;oBACnCjD,eAAe;wBAAE8C,YAAY;wBAAGC,QAAQ5F;oBAAU;gBACpD;YACF,OAAO,IAAIvC,WAAW;gBACpB,MAAMsI,qBAAqBrH,IAAAA,2BAAc,EAACjE,KAAK;gBAE/CoI,eAAe;oBACb8C,YACE,OAAOI,uBAAuB,cAAc,IAAIA;oBAClDH,QAAQ5F;gBACV;YACF,OAAO,IAAItC,WAAW;gBACpBmF,eAAe;oBAAE8C,YAAY;oBAAGC,QAAQ5F;gBAAU;YACpD,OAAO,IAAI6F,OAAOhD,YAAY,EAAE;gBAC9B,wEAAwE;gBACxE,oBAAoB;gBACpB,IAAI,OAAOgD,OAAOhD,YAAY,CAAC8C,UAAU,KAAK,UAAU;wBAQ5CE;oBAPV,IAAIA,OAAOhD,YAAY,CAAC8C,UAAU,GAAG,GAAG;wBACtC,MAAM,qBAEL,CAFK,IAAIK,MACR,CAAC,2CAA2C,EAAEH,OAAOhD,YAAY,CAAC8C,UAAU,CAAC,IAAI,CAAC,GAD9E,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBACA9C,eAAe;wBACb8C,YAAYE,OAAOhD,YAAY,CAAC8C,UAAU;wBAC1CC,QAAQC,EAAAA,uBAAAA,OAAOhD,YAAY,qBAAnBgD,qBAAqBD,MAAM,KAAIlJ,WAAWuJ,UAAU;oBAC9D;gBACF,OAAO;oBACL,oBAAoB;oBACpBpD,eAAe;wBACb8C,YAAYO,0BAAc;wBAC1BN,QAAQ5F;oBACV;gBACF;YACF;YAEA,2DAA2D;YAC3D,6DAA6D;YAC7D,IAAI6C,gBAAgB,CAACnI,IAAIoL,SAAS,CAAC,kBAAkB;gBACnDpL,IAAI4K,SAAS,CAAC,iBAAiBa,IAAAA,mCAAqB,EAACtD;YACvD;YAEA,sBAAsB;YACtB,IAAI,CAACgD,OAAO9C,KAAK,EAAE;oBAQf8C;gBAPF,oDAAoD;gBACpD,qDAAqD;gBACrD,4DAA4D;gBAC5D,2BAA2B;gBAC3BO,IAAAA,2BAAc,EACZ3L,KACA,uBACAoL,wBAAAA,OAAOhD,YAAY,qBAAnBgD,sBAAqBF,UAAU;gBAGjCjL,IAAIU,UAAU,GAAG;gBAEjB,IAAIoC,mBAAmB;oBACrB9C,IAAIW,GAAG,CAAC;oBACR;gBACF;gBACA,4DAA4D;gBAC5D,IAAIoB,uCAAAA,oBAAqB4J,SAAS,EAAE;oBAClC,MAAM5J,oBAAoB4J,SAAS,CAAC5L,KAAKC,KAAKkB,WAAW;gBAC3D,OAAO;oBACLlB,IAAIW,GAAG,CAAC;gBACV;gBACA;YACF;YAEA,IAAIwK,OAAO9C,KAAK,CAACzJ,IAAI,KAAK2J,8BAAe,CAACC,QAAQ,EAAE;gBAClD,IAAI1F,mBAAmB;oBACrB9C,IAAI4K,SAAS,CAAC,gBAAgB;oBAC9B5K,IAAIW,GAAG,CAACiL,KAAKC,SAAS,CAACV,OAAO9C,KAAK,CAACI,KAAK;oBACzC;gBACF,OAAO;oBACL,MAAMqD,iBAAiB,CAACpD;wBACtB,MAAMqD,WAAW;4BACfC,aAAatD,SAASuD,SAAS,CAACC,YAAY;4BAC5CxL,YAAYgI,SAASuD,SAAS,CAACE,mBAAmB;4BAClDvF,UAAU8B,SAASuD,SAAS,CAACG,sBAAsB;wBACrD;wBACA,MAAM1L,aAAa2L,IAAAA,iCAAiB,EAACN;wBACrC,MAAM,EAAEnF,QAAQ,EAAE,GAAG5E;wBAErB,IACE4E,YACAmF,SAASnF,QAAQ,KAAK,SACtBmF,SAASC,WAAW,CAACM,UAAU,CAAC,MAChC;4BACAP,SAASC,WAAW,GAAG,GAAGpF,WAAWmF,SAASC,WAAW,EAAE;wBAC7D;wBAEA,IAAID,SAASC,WAAW,CAACM,UAAU,CAAC,MAAM;4BACxCP,SAASC,WAAW,GAAGO,IAAAA,gCAAwB,EAC7CR,SAASC,WAAW;wBAExB;wBAEAhM,IAAIU,UAAU,GAAGA;wBACjBV,IAAI4K,SAAS,CAAC,YAAYmB,SAASC,WAAW;wBAC9C,IAAItL,eAAe8L,sCAAkB,CAACC,iBAAiB,EAAE;4BACvDzM,IAAI4K,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEmB,SAASC,WAAW,EAAE;wBAC1D;wBACAhM,IAAIW,GAAG,CAACoL,SAASC,WAAW;oBAC9B;oBACA,MAAMF,eAAeX,OAAO9C,KAAK,CAACI,KAAK;oBACvC,OAAO;gBACT;YACF;YAEA,IAAI0C,OAAO9C,KAAK,CAACzJ,IAAI,KAAK2J,8BAAe,CAACzJ,KAAK,EAAE;gBAC/C,MAAM,qBAEL,CAFK,IAAIwM,MACR,CAAC,0DAA0D,CAAC,GADxD,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,oDAAoD;YACpD,IAAIpN,YAAYgF,KAAK,EAAE;gBACrBlD,IAAI4K,SAAS,CAAC,iBAAiB;YACjC;YAEA,oCAAoC;YACpC,IAAInJ,aAAa;gBACfzB,IAAI4K,SAAS,CACX,iBACA;YAEJ;YAEA,0DAA0D;YAC1D,2BAA2B;YAC3B,IACE5G,IAAAA,2BAAc,EAACjE,KAAK,wBACnBkD,eACCe,IAAAA,2BAAc,EAACjE,KAAK,kBACpBC,IAAIU,UAAU,KAAK,KACrB;gBACA,OAAO;YACT;YAEA,MAAMgM,IAAAA,6BAAgB,EAAC;gBACrB3M;gBACAC;gBACA,6DAA6D;gBAC7D,UAAU;gBACVmL,QACErI,qBAAqB,CAACG,eAAe,CAACD,YAClC,IAAI6H,qBAAY,CACdC,OAAOC,IAAI,CAACa,KAAKC,SAAS,CAACV,OAAO9C,KAAK,CAACK,QAAQ,IAChD;oBACEsC,aAAa;oBACb9C,UAAUiD,OAAO9C,KAAK,CAACO,IAAI,CAACV,QAAQ;gBACtC,KAEFiD,OAAO9C,KAAK,CAACO,IAAI;gBACvB+D,eAAe3K,WAAW2K,aAAa;gBACvCC,iBAAiB5K,WAAW4K,eAAe;gBAC3CzE,cAAcjK,YAAYgF,KAAK,GAAGoC,YAAY6C;gBAC9C0E,MAAM/J,oBAAoB,SAAS;YACrC;QACF;QAEA,oDAAoD;QACpD,yDAAyD;QACzD,IAAIqB,YAAY;YACd,MAAMO;QACR,OAAO;YACL,MAAMT,OAAO6I,qBAAqB,CAAC/M,IAAIgE,OAAO,EAAE,IAC9CE,OAAO8I,KAAK,CACV5D,yBAAc,CAACC,aAAa,EAC5B;oBACE4D,UAAU,GAAG3I,OAAO,CAAC,EAAEtE,IAAI2J,GAAG,EAAE;oBAChC9K,MAAMqO,gBAAQ,CAACC,MAAM;oBACrBC,YAAY;wBACV,eAAe9I;wBACf,eAAetE,IAAI2J,GAAG;oBACxB;gBACF,GACAhF;QAGN;IACF,EAAE,OAAOiD,KAAK;QACZ,MAAMzJ,YAAY0L,cAAc,CAC9B7J,KACA4H,KACA;YACEkC,YAAY;YACZC,WAAW3J;YACX4J,WAAW;YACXC,kBAAkBC,IAAAA,0BAAmB,EAAC;gBACpCC,cAAc5H;gBACdZ;YACF;QACF,GACAK;QAGF,mDAAmD;QACnD,MAAM4F;IACR;AACF", "ignoreList": [0]}