'use client';

import Image from 'next/image';
import Link from 'next/link';
import { CONTACT_INFO, SOCIAL_LINKS } from '@/utils/constants';
import { scrollToTop } from '@/utils/helpers';

const Footer = () => {
  const handleLogoClick = () => {
    scrollToTop();
  };

  return (
    <footer className="footer-page-body">
      <div className="main-container">
        <hr className="border-gray-300 w-11/12 mx-auto mb-5" />
        
        <div className="footer-info">
          <Image 
            src="/logo_footer.png" 
            alt="logo_footer" 
            width={120}
            height={40}
            className="logo_top cursor-pointer" 
            onClick={handleLogoClick}
          />

          <div className="footer-address">
            <p>Madad FinTech is developing its product, and not yet live.</p>
            <p>
              Address: 201-42, Kate Business Center, Al Bustan Building,<br />
              Al Sadd, Doha, Qatar
            </p>
          </div>

          <div className="footer-social">
            {SOCIAL_LINKS.map((social) => (
              <Link 
                key={social.platform}
                href={social.url} 
                target="_blank" 
                rel="noopener noreferrer"
              >
                <Image 
                  src={social.icon} 
                  alt={social.platform}
                  width={30}
                  height={30}
                />
              </Link>
            ))}
          </div>
        </div>

        <hr className="border-gray-300 w-11/12 mx-auto my-5" />
        
        <div className="footer-copyright">
          <p>
            &copy; <strong>All rights reserved by Madad Financial Technologies 2025</strong>
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
