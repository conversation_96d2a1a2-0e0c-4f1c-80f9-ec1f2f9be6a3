'use client';

import { EXTERNAL_LINKS } from '@/utils/constants';
import { openExternalLink } from '@/utils/helpers';

interface JoinWaitlistButtonProps {
  className?: string;
  children?: React.ReactNode;
}

const JoinWaitlistButton = ({ 
  className = 'input_email_submit_button', 
  children = 'Join Waitlist' 
}: JoinWaitlistButtonProps) => {
  const handleClick = () => {
    openExternalLink(EXTERNAL_LINKS.WAITLIST_FORM);
  };

  return (
    <button 
      onClick={handleClick}
      className={className}
      type="button"
    >
      {children}
    </button>
  );
};

export default JoinWaitlistButton;
