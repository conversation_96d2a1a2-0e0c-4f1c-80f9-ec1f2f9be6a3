'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { NAV_ITEMS, EXTERNAL_LINKS } from '@/utils/constants';
import { openExternalLink } from '@/utils/helpers';

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [showSandboxBanner, setShowSandboxBanner] = useState(false);

  useEffect(() => {
    // Show sandbox banner only on home page
    const path = window.location.pathname;
    if (path === '/' || path === '/index.html') {
      setShowSandboxBanner(true);
    }
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleJoinWaitlist = () => {
    openExternalLink(EXTERNAL_LINKS.WAITLIST_FORM);
    setIsMobileMenuOpen(false);
  };

  return (
    <header className="bg-white header">
      {/* Sandbox Banner */}
      {showSandboxBanner && (
        <div id="sandboxBanner" className="bg-yellow-50 text-dark-green text-center py-2.5 w-full shadow-sm">
          <div className="flex items-center justify-center gap-2.5 max-w-6xl mx-auto px-4">
            <Image 
              src="/loudspeaker.png" 
              alt="Sandbox" 
              width={24} 
              height={24} 
              className="flex-shrink-0"
            />
            <p className="text-sm font-medium leading-relaxed text-left break-words">
              Madad is a part of Qatar Central Bank's regulatory Sandbox and considered as an authorised participant.
            </p>
          </div>
        </div>
      )}

      <div className="nav-wrapper">
        <nav className="container mx-auto flex items-center justify-between navbar main-container py-4">
          <div className="flex items-center">
            <Link href="/">
              <Image 
                src="/logo_top.png" 
                alt="madad_green_logo_navbar" 
                width={120}
                height={40}
                className="logo_top"
              />
            </Link>
            
            {/* Desktop Menu */}
            <ul className="hidden md:flex space-x-8 ml-8 font-bold">
              {NAV_ITEMS.map((item) => (
                <li key={item.href}>
                  <Link href={item.href} className="nav-link">
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Language and Button */}
          <div className="flex items-center space-x-4">
            <button 
              onClick={handleJoinWaitlist}
              className="nav-button hidden md:flex"
              type="button"
            >
              Join Waitlist
            </button>

            {/* Hamburger Menu Icon */}
            <button 
              id="hamburger" 
              onClick={toggleMobileMenu}
              className="md:hidden flex flex-col items-center justify-center w-10 h-10 rounded focus:outline-none"
            >
              <span className="bg-gray-600 block w-full h-1 mb-1"></span>
              <span className="bg-gray-600 block w-full h-1 mb-1"></span>
              <span className="bg-gray-600 block w-full h-1"></span>
            </button>
          </div>
        </nav>
      </div>

      {/* Mobile Menu */}
      <div 
        id="mobile-menu" 
        className={`${isMobileMenuOpen ? 'block' : 'hidden'} bg-white shadow-lg md:hidden`}
      >
        <ul className="flex flex-col pl-8 py-4 space-y-6">
          {NAV_ITEMS.map((item) => (
            <li key={item.href}>
              <Link 
                href={item.href} 
                className="nav-link font-bold"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {item.label}
              </Link>
            </li>
          ))}
          <li>
            <button 
              onClick={handleJoinWaitlist}
              className="input_email_submit_button"
            >
              Join Waitlist
            </button>
          </li>
        </ul>
      </div>
    </header>
  );
};

export default Header;
