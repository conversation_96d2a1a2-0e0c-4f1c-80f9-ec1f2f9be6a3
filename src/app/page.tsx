'use client';

import { useEffect } from 'react';
import Image from 'next/image';
import { FeatureCard, ProcessStep, Accordion, JoinWaitlistButton, ContactSection, StructuredData } from '@/components';
import { FEATURE_CARDS, PROCESS_STEPS, FAQ_ITEMS } from '@/utils/constants';
import { initEmailJS } from '@/lib/emailjs';
import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import { generateStructuredData } from '@/utils/seo';

export default function Home() {
  const { activeSteps } = useScrollAnimation();

  useEffect(() => {
    // Initialize EmailJS when component mounts
    initEmailJS();
  }, []);

  // Generate FAQ structured data
  const faqStructuredData = generateStructuredData.faq(
    FAQ_ITEMS.map(item => ({
      question: item.question,
      answer: item.answer
    }))
  );

  return (
    <>
      <StructuredData data={generateStructuredData.service()} />
      <StructuredData data={faqStructuredData} />
      {/* Hero Section */}
      <main className="container mx-auto hero-section main-container">
        <div className="hero-grid">
          <div className="hero-text">
            <h1>Get Cash Today, Against Due Invoices</h1>
            <p className="waiting-paragraph">Struggling with delayed invoice payments?</p>
            <p className="wait-no-more">
              Say goodbye to delays&nbsp;-&nbsp;discount your unpaid invoices for instant cash
            </p>

            <div className="flex form-container-hero">
              <JoinWaitlistButton />
            </div>
            <div className="form_error_message"></div>
          </div>

          <div className="hero-image">
            <Image
              src="/hero_image.png"
              alt="Invoice Discounting Illustration"
              width={500}
              height={400}
              priority
            />
          </div>
        </div>

        <hr className="max-w-2xl mx-auto mt-15" />
        <footer className="text-dark-green text-center py-4">
          <br />
          <p className="coming_soon_in_qatar">Coming Soon in Qatar!</p>
        </footer>
      </main>

      {/* Features Section */}
      <div className="w-full bg-gray-50">
        <div className="features_container main-container">
          <h1>Transform the way you finance your business.</h1>
          <div className="features-grid">
            {FEATURE_CARDS.map((feature) => (
              <FeatureCard key={feature.id} feature={feature} />
            ))}
          </div>
        </div>
      </div>

      {/* Process Section */}
      <section className="section-3">
        <div className="container main-container section3_main_container">
          <h1 className="title">
            A seamless, quick and hassle-free process to<br /> get access to cash
          </h1>

          <div className="process">
            <div className="line-container">
              <div className="dashed-line"></div>
              <div className="solid-line"></div>
              <div className="arrow"></div>
            </div>

            {PROCESS_STEPS.map((step) => (
              <ProcessStep
                key={step.id}
                step={step}
                isActive={activeSteps.includes(step.id)}
              />
            ))}

            <div className="step" data-step="7">
              <div className="circle"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Invoice Discounting Explanation */}
      <div className="bg-gray-50 w-full">
        <section className="invoice-discounting-container main-container">
          <h1>What is Invoice Discounting?</h1>
          <p>
            When you send an invoice to a customer, you have to wait 30, 60, 90 or even 120 days to get paid.
          </p>
          <p>
            But with invoice discounting on Madad, you don&apos;t have to wait! We give you most of the invoice amount
            upfront, so you can use that money to keep your business running smoothly—whether it&apos;s paying bills, or taking
            on new projects.
          </p>
          <p>
            Once your customer pays the invoice, the rest is settled, and you&apos;re ready to move forward, without any
            complicated process or paperwork.
          </p>
        </section>

        {/* FAQ Section */}
        <section className="invoice-discounting-container FAQs">
          <h1 className="main-container">Frequently Asked Questions</h1>
          <Accordion items={FAQ_ITEMS} />
        </section>
      </div>

      {/* Call to Action */}
      <div className="bg-gray-50 py-5 text-center">
        <div>
          <h2 className="text-dark-green text-xl mb-4">Don&apos;t miss the opportunity to get funded</h2>
          <div className="flex flex-col items-center">
            <JoinWaitlistButton className="bg-dark-green text-white border-none py-2.5 px-5 text-base rounded cursor-pointer transition-colors hover:bg-gray-800" />
          </div>
          <div className="form_error_message text-red-600 text-sm mt-2.5"></div>
        </div>
      </div>

      {/* Contact Section */}
      <ContactSection />
    </>
  );
}
