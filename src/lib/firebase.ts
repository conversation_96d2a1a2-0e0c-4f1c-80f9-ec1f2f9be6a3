import { initializeApp, getApps, getApp } from 'firebase/app';
import { getAnalytics, Analytics, isSupported } from 'firebase/analytics';
import { FirebaseConfig } from '@/types';

const firebaseConfig: FirebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN!,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET!,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID!,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID!,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID!
};

// Initialize Firebase (avoid multiple initialization)
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApp();

// Initialize Analytics (only on client side and when supported)
let analytics: Analytics | null = null;

const initializeAnalytics = async () => {
  if (typeof window !== 'undefined' && !analytics) {
    try {
      const supported = await isSupported();
      if (supported) {
        analytics = getAnalytics(app);
      }
    } catch (error) {
      console.warn('Firebase Analytics not supported:', error);
    }
  }
  return analytics;
};

// Export functions and instances
export { app, analytics, initializeAnalytics };

// Firebase services initialization
export const firebaseServices = {
  app,
  analytics: null as Analytics | null,

  async initialize() {
    this.analytics = await initializeAnalytics();
    return {
      app: this.app,
      analytics: this.analytics
    };
  }
};
