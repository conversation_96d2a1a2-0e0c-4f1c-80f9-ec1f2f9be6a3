* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.main-container {
    max-width: 1440px;
    /* Maximum width for large screens */
    width: 100%;
    /* Full width on smaller screens */
    margin: 0 auto;
    /* Centers the container */
    padding: 0 20px;
    /* Adds padding to the sides */
}

body {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    /* font-family: Inter, sans-serif; */
}

.globe_image {
    height: 67px;
    width: 67px;
}

/* Logo */
.logo_top {
    cursor: pointer;
    height: 90px;
    width: 90px;
}

/* Header */
.nav-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.coming_soon_in_qatar {
    /* font-family: Inter; */
    font-size: 40px;
    font-weight: 700;
    line-height: 48.41px;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #208039;
}

.navbar {
    max-width: 1440px;
    height: 94px;
    width: 100%;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-link {
    color: #004141;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-link:hover {
    color: #208039;
}

.nav-button {
    background: #004141;
    color: #fff;
    padding: 0.5rem 1.5rem;
    border-radius: 5px;
    transition: background 0.3s;
}

.nav-button:hover {
    background: #002c2c;
}

/* Hamburger Menu */
#hamburger {
    display: none;
}

@media (max-width: 780px) {
    #hamburger {
        display: flex;
    }

    .nav-wrapper ul {
        display: none;
    }
}

.hero-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center !important;
    padding: 2rem 1rem;
}

.hero-text h1 {
    max-width: 477px;
    /* font-family: Inter; */
    font-size: 45px;
    font-weight: 700;
    line-height: 54.46px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;

    /* font-size: 2.5rem; */
    color: #004141;
}


.hero-text p:first-of-type {
    max-width: 477px;
    /* font-family: Inter; */
    font-size: 18px;
    font-weight: 700;
    line-height: 19.36px;
    text-align: left;
    margin-top: 40px;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;

    margin: 1rem 0;
    color: #767676;
}

.hero-text p:last-of-type {
    margin-top: 12px;
    max-width: 477px;
    /* font-family: Inter; */
    font-size: 17px;
    font-weight: 400;
    line-height: 19.36px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #767676;

}

.error-message {
    color: red;
    font-size: 12px;
    margin-top: 5px;
    display: block;
    /* Ensures it sits on its own line */
}


.input_email_header {
    margin-top: 23px;
    border: 1px solid #ccc;
    padding: 0.5rem;
    border-radius: 5px 0 0 5px;
    flex: 0.6;
}

.input_email_submit_button {
    margin-top: 23px;
    background: #004141;
    color: #fff;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0 5px 5px 0;
    cursor: pointer;
}

.input_email_submit_button:hover {
    background: #208039;
}

.form_error_message {
    color: red;
    font-size: 12px;
    margin-top: 8px;
    text-align: left;
}

.hero-image img {
    width: 100%;
    max-width: 700px;
}


.invoice-discounting-container {
    display: flex;
    margin-top: 70px;
    justify-content: center;
    align-items: center;
    max-width: 1440px;
    min-height: 400px;
    margin-left: auto;
    margin-right: auto;
    flex-direction: column;
    align-items: start;
    background-color: #f9f9f9;
}

.invoice-discounting-container>h1 {
    margin-top: 30px !important;
}

.FAQs {
    background-color: #ffffff;
    margin-top: -70px !important;
}

h1 {
    /* font-family: Inter; */
    font-size: 32px;
    font-weight: 700;
    line-height: 38.73px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #004141;
    margin-bottom: 26px;
}

.invoice-discounting-container p {
    /* font-family: Inter; */
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    text-align: justified;
    margin-bottom: 26px;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;

}

@media (max-width: 768px) {
    .content {
        max-width: 100%;
    }

    h1 {
        font-size: 24px;
    }

    p {
        font-size: 14px;
    }
}

@media (max-width: 780px) {
    .hero-grid {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-text {
        margin-left: auto;
        margin-right: auto;
        order: 2;
    }

    .hero-image {
        order: 1;
    }
}


.accordion {
    width: 100%;
}

.accordion-item {
    margin-bottom: 10px;
    border-radius: 5px;
}

.accordion-header {
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    border-radius: 5px 5px 0px 0px;
    border: 1px solid #d8d8d8 !important;
    border-bottom: none;
    background: #f9f9f9 !important;
    width: 100%;
    text-align: left;
    font-size: 15px;
    font-weight: 500;
    color: #004141;
    transition: background-color 0.2s;
}

.accordion-header:hover {
    background-color: #F9FAFB;
}

.accordion-content {
    max-height: 0;
    border-radius: 5px 5px;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
    border: 1px solid #d8d8d8;
    border-top: none;
    background-color: #fff;
}

.accordion-content p {
    margin-top: auto;
    padding: 30px 30px 16px;
    color: #000000;
    font-size: 14px;
    line-height: 1.6;
}

.accordion-icon {
    transition: transform 0.3s ease;
    stroke: #000000;
}

.accordion-item.active .accordion-icon {
    transform: rotate(180deg);

}

.accordion-item.active .accordion-content {
    max-height: 200px;
}

@media (max-width: 768px) {

    .accordion-header {
        padding: 14px 16px;
        font-size: 17px;
    }

    .accordion-content p {
        padding: 0 16px 14px;
        font-size: 15px;
    }
}

@media (max-width: 480px) {

    .invoice-discounting-container {
        margin-top: 50px !important;
        margin-bottom: 40px;
    }

    .input_email_header {
        width: 200px;
        height: 50px;
    }

    .input_email_submit_button {
        margin-left: auto;
        margin-right: auto;
    }
}

.notification {
    background-color: #f3f7f4 !important;
    text-align: center !important;
    width: 100% !important;
    min-height: 392px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.notification h2 {
    /* font-family: Inter; */
    font-size: 24px;
    font-weight: 700;
    line-height: 29.05px;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #004141;
    margin-bottom: 60px !important;
}

.notification .form-container,
.form-container-hero {
    max-width: 700px !important;
    height: 100%;
    margin: 0 auto !important;
    display: flex !important;
    flex-wrap: wrap;
}

.notification input[type="email"] {
    flex: 1 !important;
    width: 400px;
    height: 42px !important;
    padding: 0 16px !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 4px 0 0 4px !important;
    font-size: 14px !important;
    outline: none !important;
}

.form-container-hero input[type="email"] {
    flex: 1 !important;
    max-width: 400px !important;
    height: 42px !important;
    padding: 0 16px !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 4px 0 0 4px !important;
    font-size: 14px !important;
    outline: none !important;
}


.notification input[type="email"]::placeholder {
    color: #9ca3af !important;
}


.notification button,
.form-container-hero button {
    background-color: #015847 !important;
    color: white !important;
    border: none !important;
    border-radius: 0 4px 4px 0 !important;
    padding: 0 24px !important;
    height: 42px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    cursor: pointer !important;
    white-space: nowrap !important;
    transition: all 400ms;
}

.notification button:hover,
.form-container-hero .nav-button:hover {
    background-color: #014a3c !important;
}

@media (max-width: 420px) {

    .notification .form-container,
    .form-container-hero {
        justify-content: center;
        align-items: center;
        gap: 10px;
    }
}


/* features styling */

.features_container {
    max-width: 1440px;
    margin: 0 auto;
    padding: 60px 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: #F9F9F9;
}

h1 {
    /* font-family: Inter; */
    font-size: 32px;
    font-weight: 700;
    line-height: 38.73px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    margin-bottom: 48px;
    color: #000000;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    padding: 0 20px;
}

.feature-card {
    background: #FFFFFF;
    padding: 32px 24px;
    border-radius: 5px;
    box-shadow: 0px 2px 6px 0px #00000029;
    transition: all 400ms;
}

.feature-card:hover {
    /* cursor: pointer; */
    background: #fafafa;
    transform: scale(1.02);
}


.icon-wrapper {
    margin-left: auto;
    margin-right: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 156px;
    width: 156px;
    flex-direction: column;
    gap: 19px;
    /* Adjust to the desired size */
    background-color: #e0ebd7;
    /* Set to the background color you want */
    border-radius: 50%;
    position: relative;
}

.qatari_msme_group {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    gap: 0px;
}

/* .msme_group_image1 {} */


.msme_group_image2 {
    align-self: center;
    margin-top: 31px;
    margin-left: -14px;
}

.image_feature_card_2 {
    background-image: url("../images/multiple_fin.png");
    background-repeat: no-repeat;
    background-position: center;
}

.icon-wrapper:nth-of-type(2) {
    background-color: firebrick !important;
}

.icon {
    width: 24px;
    /* Adjust each icon size */
    height: 24px;
}

/* Positioning each icon within the circle */
.cash-icon {
    position: absolute;
    left: 12px;
    /* Adjust position to your liking */
}

.hand-icon {
    position: absolute;
    right: 12px;
    /* Adjust position to your liking */
}

.QAR {
    /* font-family: Inter; */
    font-size: 16px;
    font-weight: 600;
    line-height: 19.36px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #fff;
}

.feature-title {
    /* font-family: Inter; */
    font-size: 24px;
    font-weight: 600;
    line-height: 29.05px;
    text-align: left;
    text-underline-position: from-font;
    color: #000;
    text-decoration-skip-ink: none;

    margin-top: 29px;
    margin-bottom: 12px;
}

.feature-description {
    color: #666;
    font-size: 14px;
}

.build_ur_business {
    margin-bottom: 9px;
}

@media (max-width: 1200px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .container {
        padding: 40px 16px;
    }

    .feature-card h1 {
        font-size: 24px;
        margin-bottom: 32px;
        padding: 0 16px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0 16px;
    }

    .feature-card {
        padding: 24px 20px;
    }

    .icon-wrapper {
        width: 150px;
        height: 150px;
        margin-bottom: 20px;
    }

    .feature-title {
        font-size: 18px;
        margin-bottom: 12px;
    }
}


/* section 3 styling */


@media (max-width: 992px) {
    .section3_main_container {
        max-width: 95% !important;
        padding: 0 8px !important;
    }
}


@media (max-width: 768px) {
    .step-title {
        font-size: 24px !important;
    }
}


@media (max-width: 768px) {
    .step-content {
        flex-direction: column !important;
        align-items: center !important;
        text-align: left !important;
    }
}


@media (max-width: 768px) {

    .step-left,
    .step-right {
        margin-bottom: 10px !important;
        text-align: left !important;
    }
}

@media (max-width: 550px) {

    .step-left,
    .step-right {
        width: 90% !important;
        margin: 0 auto !important;
    }
}




@media (max-width: 768px) {
    .title {
        font-size: 28px !important;
        line-height: 1.4 !important;
    }
}



@media (max-width: 768px) {
    .benefits p {
        padding-left: 0 !important;
        text-align: left !important;
        font-size: 20px !important;
    }
}

@media (max-width: 480px) {
    .benefits p {
        font-size: 16px !important;
    }
}


@media (max-width: 768px) {
    .subtitle {
        font-size: 18px !important;
        text-align: center !important;
    }
}



@media (max-width: 768px) {
    .list li {
        font-size: 2rem important;
        text-align: left !important;
    }
}



@media (max-width: 768px) {
    .step {
        padding: 15px !important;
    }
}



#toast-container {
    position: fixed;
    z-index: 99999;
    display: none;
    /* Initially hidden */
    padding: 2px 3px;
    justify-content: space-between;
    align-items: center;
    background: rgb(233, 242, 235) !important;
    border: 1px solid #208039 !important;
    border-radius: 5px !important;
    height: 54px;
    width: 683px;
    top: 20px;
    right: 20px;
    transform: translateX(100%);
    animation: slideIn 0.5s ease-in-out forwards;
}

#toast-container .icon_message_container {
    display: flex;
    align-items: center;
    margin-right: 10px;
}

#toast-container .icon_message_container .toast_right_icon {
    height: 26px;
    width: 26px;
    margin-left: 10px;
}

#toast-container .icon_message_container p {
    font-size: 14px;
    margin-left: 10px;
}

#toast-container .close_icon {
    margin-right: 10px;
    cursor: pointer;
}

#toast-container.show-toast {
    display: flex;
    /* Show the toast */
    right: 20px;
    /* Move toast to the screen */
    opacity: 1;
    transition: opacity 1s ease-in-out, right 1s ease-in-out;
}

@keyframes slideIn {
    from {
        right: -400px;
        /* Starting position off-screen */
        transform: translateX(100%);
        /* Off-screen to the right */
    }

    to {
        right: 20px;
        /* Final position on-screen */
        transform: translateX(0);
        /* Center the toast */
    }
}

@media (max-width: 480px) {

    #toast-container {
        padding: 2px 3px;
        justify-content: space-between;
        /* margin-inline: 10px; */
        align-items: center;
        height: 54px;
        width: 80%;
        position: fixed;
        top: -54px;
        margin: 0 auto;
        left: 50%;
        transform: translateX(-50%);
        animation: slideIn 0.5s ease-in-out forwards;
        transition: top 0.5s ease-in-out;
        z-index: 1000;
    }

    #toast-container .icon_message_container {
        margin-right: 0px;
    }

    #toast-container .icon_message_container .toast_right_icon {
        height: 20px;
        width: 20px;
        margin-left: 8px;
    }

    #toast-container .icon_message_container p {
        font-size: 10px;
        margin-left: 8px;
    }

    #toast-container .close_icon {
        margin-right: 10px;
    }

    #toast-container .show-toast {
        top: 20px;
        /* transition: opacity 1s ease-in-out, right 1s ease-in-out; */
    }

    @keyframes slideIn {
        from {
            top: -54px;
            /* Starting position above the viewport */
        }

        to {
            top: 20px;
            /* Final position within the viewport */
        }
    }
}


@media (min-width: 481px) and (max-width: 768px) {

    #toast-container {
        padding: 2px 3px;
        justify-content: space-between;
        align-items: center;
        height: 54px;
        width: 80%;
        position: fixed;
        top: -54px;
        margin: 0 auto;
        left: 50%;
        transform: translateX(-50%);
        animation: slideIn 0.5s ease-in-out forwards;
        transition: top 0.5s ease-in-out;
        z-index: 1000;
    }

    #toast-container .icon_message_container {
        margin-right: 0px;
    }

    #toast-container .icon_message_container .toast_right_icon {
        height: 24px;
        width: 24px;
        margin-left: 8px;
    }

    #toast-container .icon_message_container p {
        font-size: 12px;
        margin-left: 8px;
    }

    #toast-container .close_icon {
        margin-right: 10px;
    }

    #toast-container .show-toast {
        top: 20px;
        /* transition: opacity 1s ease-in-out, right 1s ease-in-out; */
    }

    @keyframes slideIn {
        from {
            top: -54px;
            /* Starting position above the viewport */
        }

        to {
            top: 20px;
            /* Final position within the viewport */
        }
    }
}


@media (min-width: 769px) and (max-width: 992px) {

    #toast-container {
        width: 55%;
        height: 50px;
    }

    #toast-container .icon_message_container .toast_right_icon {
        height: 24px;
        width: 24px;
        margin-left: 8px;
    }

    #toast-container .icon_message_container p {
        font-size: 12px;
        margin-left: 8px;
    }
}