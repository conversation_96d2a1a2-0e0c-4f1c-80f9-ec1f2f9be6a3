* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.main-container {
    max-width: 1440px;
    /* Maximum width for large screens */
    width: 100%;
    /* Full width on smaller screens */
    margin: 0 auto;
    /* Centers the container */
    padding: 0 20px;
    /* Adds padding to the sides */
}

body {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    /* font-family: Inter, sans-serif; */
}

.globe_image {
    height: 67px;
    width: 67px;
}

/* Logo */
.logo_top {
    cursor: pointer;
    height: 90px;
    width: 90px;
}

/* Header */
.nav-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.coming_soon_in_qatar {
    /* font-family: Inter; */
    font-size: 40px;
    font-weight: 700;
    line-height: 48.41px;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #208039;
}

.navbar {
    max-width: 1440px;
    height: 94px;
    width: 100%;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-link {
    color: #004141;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-link:hover {
    color: #208039;
}

.nav-button {
    background: #004141;
    color: #fff;
    padding: 0.5rem 1.5rem;
    border-radius: 5px;
    transition: background 0.3s;
}

.nav-button:hover {
    background: #002c2c;
}

/* Hamburger Menu */
#hamburger {
    display: none;
}

@media (max-width: 780px) {
    #hamburger {
        display: flex;
    }

    .nav-wrapper ul {
        display: none;
    }
}

.hero-text h1 {
    max-width: 477px;
    /* font-family: Inter; */
    font-size: 45px;
    font-weight: 700;
    line-height: 54.46px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;

    /* font-size: 2.5rem; */
    color: #004141;
}


.hero-text p:first-of-type {
    max-width: 477px;
    /* font-family: Inter; */
    font-size: 18px;
    font-weight: 700;
    line-height: 19.36px;
    text-align: left;
    margin-top: 40px;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;

    margin: 1rem 0;
    color: #767676;
}

.hero-text p:last-of-type {
    margin-top: 12px;
    max-width: 477px;
    /* font-family: Inter; */
    font-size: 17px;
    font-weight: 400;
    line-height: 19.36px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #767676;

}

.error-message {
    color: red;
    font-size: 12px;
    margin-top: 5px;
    display: block;
    /* Ensures it sits on its own line */
}


.input_email_header {
    margin-top: 23px;
    border: 1px solid #ccc;
    padding: 0.5rem;
    border-radius: 5px 0 0 5px;
    flex: 0.6;
}

.input_email_submit_button {
    margin-top: 23px;
    background: #004141;
    color: #fff;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0 5px 5px 0;
    cursor: pointer;
}

.input_email_submit_button:hover {
    background: #208039;
}

.form_error_message {
    color: red;
    font-size: 12px;
    margin-top: 8px;
    text-align: left;
}

.hero-image img {
    width: 100%;
    max-width: 700px;
}


.invoice-discounting-container {
    display: flex;
    margin-top: 70px;
    justify-content: center;
    align-items: center;
    max-width: 1440px;
    min-height: 400px;
    margin-left: auto;
    margin-right: auto;
    flex-direction: column;
    align-items: start;
    background-color: #f9f9f9;
}

.invoice-discounting-container>h1 {
    margin-top: 30px !important;
}

.FAQs {
    background-color: #ffffff;
    margin-top: -70px !important;
}

h1 {
    /* font-family: Inter; */
    font-size: 32px;
    font-weight: 700;
    line-height: 38.73px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #004141;
    margin-bottom: 26px;
}

.invoice-discounting-container p {
    /* font-family: Inter; */
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    text-align: justified;
    margin-bottom: 26px;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;

}

@media (max-width: 768px) {
    .content {
        max-width: 100%;
    }

    h1 {
        font-size: 24px;
    }

    p {
        font-size: 14px;
    }
}

@media (max-width: 780px) {
    .hero-grid {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-text {
        margin-left: auto;
        margin-right: auto;
        order: 2;
    }

    .hero-image {
        order: 1;
    }
}

.hero-grid {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 5rem;
}

.hero-section {
    background-image: url('../images/AboutUs_Background.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 100vh;
}

.hero-content {
    display: flex;
    /* align-items: center; */
    flex-direction: column;
    /* justify-content: center; */
    padding-top: 2.2rem;
}

.hero-content h1 {
    color: white !important;
    font-size: 48px;
    font-weight: 700;
    text-align: center;
}

.hero-content p {
    color: white !important;
    font-size: 48px;
    font-weight: 700;
    /* line-height: 54.46px; */
    text-align: center;
}

.we_belive_container {
    display: grid;
    padding: 4rem;
    grid-template-columns: repeat(2, 1fr);
}

.we_belive_container img {
    width: 600px;
    height: 400px;
}

.we_belive_container .we_belive_content {
    margin-inline: 4rem;
}

.we_belive_container .we_belive_content .we_belive_header {
    color: #208039;
    font-weight: 700;
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.we_belive_header h3 {
    position: relative;
    font-size: 1.5rem;
    padding-left: 1rem;
    line-height: 1;
}

.we_belive_container .we_belive_content .we_belive_header h3::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    height: 100%;
    /* Match height to text */
    background-color: #004141;
}

.we_belive_content h2 {
    font-size: 2rem;
    color: #323232;
    font-weight: 700;
    margin-bottom: 1rem;
}

.we_belive_content .we_belive_para p {
    font-weight: 400;
    font-size: 14px;
    color: #4B4B4B;
    margin-bottom: 1rem;
}

.hero-3-section {
    background-image: url('../images/Mask_group.png');
    background-size: cover;
    width: 100%;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 100vh;
}

/* .how_it_started {
    margin-inline: 2.5rem;
    width: 45%;
    background: linear-gradient(to bottom, #FFFFFF 40%, #FFFFFF 80%);
    border-radius: 5px;
    padding: 2rem;
    position: relative;
    top: 5rem;
} */

.how_it_started {
    margin-inline: 2.5rem;
    width: 50%;
    /* padding-top: 4rem; */
    position: relative;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 1;
    padding: 2.5rem;
    border-radius: 5px;
    top: 4.5rem;
}

.how_it_started .how_it_started_header {
    color: #208039;
    font-weight: 700;
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.how_it_started_header h3 {
    position: relative;
    font-size: 1.5rem;
    padding-left: 1rem;
    line-height: 1;
}

.how_it_started .how_it_started_header h3::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    height: 100%;
    /* Match height to text */
    background-color: #004141;
}

.how_it_started h2 {
    font-size: 28px;
    color: #323232;
    font-weight: 700;
    margin-bottom: 1rem;
}

.how_it_started .how_it_started_para p {
    font-weight: 400;
    font-size: 14px;
    color: #4B4B4B;
    margin-bottom: 1rem;
}

.hero-4-section {
    width: 100%;
}

.vision_mission {
    display: flex;
    flex-direction: row;
    width: 100%;
}

.vision_mission img {
    height: 100%;
    object-fit: cover;
}

.vision_mission_content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: #EAEAEA;
}

.vision_mission_content hr {
    border: 1px solid #ABABAB;
    width: 85%;
    margin: 0 auto;
}

.vision_mission_para {
    flex: 1;
    align-content: center;
    padding: 3rem;
    text-align: center;
}

.vision_mission_para h4 {
    font-weight: 700;
    font-size: 24px;
    margin-bottom: 1rem;
}

.vision_mission_para p {
    font-weight: 400;
    font-size: 26px;
}


.hero-5-section {
    background-color: #FAFAFA;
    padding: 4rem;
}

.core_principles h3 {
    font-size: 30px;
    font-weight: 700;
    margin-bottom: 2rem;
}

.core_principles_cont {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: 2rem;
    row-gap: 3rem;
}

.core_principles_box {
    display: flex;
    background-color: #FFFFFF;
    flex-direction: column;
    border-radius: 5px;
    box-shadow: 0px 2px 6px 0px #00000029;
    transition: all 400ms;
}

.core_principles_box:hover {
    transform: scale(1.02);
}

.core_principles_content {
    padding-inline: 1rem;
    padding-block: 1.5rem;
}

.core_principles_head {
    display: flex;
    align-items: baseline;
    margin-bottom: 0.8rem;
}

.core_principles_head p {
    font-weight: 700;
    font-size: 38px;
    color: #208039;
    margin-right: 0.8rem;
}

.core_principles_head span {
    font-weight: 700;
    font-size: 24px;
    color: #323232;
}

.core_principles_para {
    color: #4B4B4B;
    font-size: 14px;
}

.hero-6-section {
    background-color: #F6F6F6;
    padding: 4rem;
}

.leadership_header {
    text-align: center;
}

.leadership_header h3 {
    font-weight: 700;
    font-size: 30px;
    color: #323232;
    margin-bottom: 1rem;
}

.leadership_header p {
    font-size: 18px;
    color: #4B4B4B;
}

.leadership_cont {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    margin-top: 3rem;
    padding-inline: 1.5rem;
    column-gap: 2.5rem;
}

.leadership_box {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.leadership_box img {
    margin-bottom: 1.5rem;
    width: 180px;
    height: 180px;
    object-fit: fill;
}

.leadership_para {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.leadership_box h4 {
    font-weight: 700;
    font-size: 24px;
    color: #323232;
    line-height: 2.5rem;
}

.leadership_box p {
    font-weight: 400;
    font-size: 22px;
    color: #323232;
    margin-bottom: 1.5rem;
}

.leadership_box span {
    font-size: 14px;
    color: #4B4B4B;
    padding-inline: 1.5rem;
}

.form-container-hero {
    max-width: 700px !important;
    height: 100%;
    margin: 0 auto !important;
    display: flex !important;
    flex-wrap: wrap;
}

.form-container-hero input[type="email"] {
    flex: 1 !important;
    max-width: 400px !important;
    height: 42px !important;
    padding: 0 16px !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 4px 0 0 4px !important;
    font-size: 14px !important;
    outline: none !important;
}

.form-container-hero button {
    background-color: #015847 !important;
    color: white !important;
    border: none !important;
    border-radius: 0 4px 4px 0 !important;
    padding: 0 24px !important;
    height: 42px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    cursor: pointer !important;
    white-space: nowrap !important;
    transition: all 400ms;
}

.form-container-hero .nav-button:hover {
    background-color: #014a3c !important;
}

#toast-container {
    position: fixed;
    z-index: 99999;
    display: none;
    /* Initially hidden */
    padding: 2px 3px;
    justify-content: space-between;
    align-items: center;
    background: rgb(233, 242, 235) !important;
    border: 1px solid #208039 !important;
    border-radius: 5px !important;
    height: 54px;
    width: 683px;
    top: 20px;
    right: 20px;
    transform: translateX(100%);
    animation: slideIn 0.5s ease-in-out forwards;
}

#toast-container .icon_message_container {
    display: flex;
    align-items: center;
    margin-right: 10px;
}

#toast-container .icon_message_container .toast_right_icon {
    height: 26px;
    width: 26px;
    margin-left: 10px;
}

#toast-container .icon_message_container p {
    font-size: 14px;
    margin-left: 10px;
}

#toast-container .close_icon {
    margin-right: 10px;
    cursor: pointer;
}

#toast-container.show-toast {
    display: flex;
    right: 20px;
    opacity: 1;
    transition: opacity 1s ease-in-out, right 1s ease-in-out;
}

@keyframes slideIn {
    from {
        right: -400px;
        transform: translateX(100%);
    }

    to {
        right: 20px;
        transform: translateX(0);
    }
}

@media (max-width: 480px) {

    #toast-container {
        padding: 2px 3px;
        justify-content: space-between;
        /* margin-inline: 10px; */
        align-items: center;
        height: 54px;
        width: 80%;
        position: fixed;
        top: -54px;
        margin: 0 auto;
        left: 50%;
        transform: translateX(-50%);
        animation: slideIn 0.5s ease-in-out forwards;
        transition: top 0.5s ease-in-out;
        z-index: 1000;
    }

    #toast-container .icon_message_container {
        margin-right: 0px;
    }

    #toast-container .icon_message_container .toast_right_icon {
        height: 20px;
        width: 20px;
        margin-left: 8px;
    }

    #toast-container .icon_message_container p {
        font-size: 10px;
        margin-left: 8px;
    }

    #toast-container .close_icon {
        margin-right: 10px;
    }

    #toast-container .show-toast {
        top: 20px;
        /* transition: opacity 1s ease-in-out, right 1s ease-in-out; */
    }

    @keyframes slideIn {
        from {
            top: -54px;
            /* Starting position above the viewport */
        }

        to {
            top: 20px;
            /* Final position within the viewport */
        }
    }
}


@media (min-width: 481px) and (max-width: 768px) {

    #toast-container {
        padding: 2px 3px;
        justify-content: space-between;
        align-items: center;
        height: 54px;
        width: 80%;
        position: fixed;
        top: -54px;
        margin: 0 auto;
        left: 50%;
        transform: translateX(-50%);
        animation: slideIn 0.5s ease-in-out forwards;
        transition: top 0.5s ease-in-out;
        z-index: 1000;
    }

    #toast-container .icon_message_container {
        margin-right: 0px;
    }

    #toast-container .icon_message_container .toast_right_icon {
        height: 24px;
        width: 24px;
        margin-left: 8px;
    }

    #toast-container .icon_message_container p {
        font-size: 12px;
        margin-left: 8px;
    }

    #toast-container .close_icon {
        margin-right: 10px;
    }

    #toast-container .show-toast {
        top: 20px;
        /* transition: opacity 1s ease-in-out, right 1s ease-in-out; */
    }

    @keyframes slideIn {
        from {
            top: -54px;
            /* Starting position above the viewport */
        }

        to {
            top: 20px;
            /* Final position within the viewport */
        }
    }
}


@media (min-width: 769px) and (max-width: 992px) {

    #toast-container {
        width: 55%;
        height: 50px;
    }

    #toast-container .icon_message_container .toast_right_icon {
        height: 24px;
        width: 24px;
        margin-left: 8px;
    }

    #toast-container .icon_message_container p {
        font-size: 12px;
        margin-left: 8px;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 24px;
    }

    .hero-content p {
        font-size: 24px;
    }

    .we_belive_container {
        padding: 1rem;
        max-width: 450px;
        grid-template-columns: repeat(1, 1fr);
    }

    .we_belive_container .we_belive_content {
        margin-inline: 1rem;
    }

    .we_belive_container .we_belive_content .we_belive_header {
        align-items: center;
        margin-bottom: 1rem;
    }

    .we_belive_header h3 {
        position: relative;
        font-size: 1rem;
        padding-left: 1rem;
        line-height: 1;
    }

    .we_belive_container .we_belive_content .we_belive_header h3::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        height: 100%;
        /* Match height to text */
        background-color: #004141;
    }

    .we_belive_content h2 {
        font-size: 1.2rem;
        color: #323232;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .we_belive_content .we_belive_para p {
        font-weight: 400;
        font-size: 12px;
        color: #4B4B4B;
        margin-bottom: 1rem;
    }
}