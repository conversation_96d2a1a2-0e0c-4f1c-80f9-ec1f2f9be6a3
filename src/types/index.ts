// Common types for the application

export interface NavItem {
  label: string;
  href: string;
  external?: boolean;
}

export interface FeatureCard {
  id: string;
  title: string;
  description: string;
  icon: string;
  imageAlt: string;
}

export interface ProcessStep {
  id: number;
  title: string;
  subtitle: string;
  items: string[];
  benefits: string[];
}

export interface AccordionItem {
  id: string;
  question: string;
  answer: string;
}

export interface ContactInfo {
  phone: string;
  email: string;
  address: string;
}

export interface SocialLink {
  platform: string;
  url: string;
  icon: string;
}

export interface FormData {
  email: string;
}

export interface EmailJSResponse {
  status: number;
  text: string;
}

export interface FirebaseConfig {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
  measurementId: string;
}

export interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  ogUrl?: string;
  canonical?: string;
}

export interface PageProps {
  params?: { [key: string]: string };
  searchParams?: { [key: string]: string | string[] | undefined };
}
