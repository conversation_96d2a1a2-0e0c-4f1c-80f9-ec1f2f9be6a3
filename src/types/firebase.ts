// Firebase-specific types and interfaces

import { Analytics } from 'firebase/analytics';
import { FirebaseApp } from 'firebase/app';

// Firebase configuration interface
export interface FirebaseConfig {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
  measurementId: string;
}

// Firebase services interface
export interface FirebaseServices {
  app: FirebaseApp;
  analytics: Analytics | null;
}

// Analytics event parameters
export interface AnalyticsEventParams {
  event_category?: string;
  event_label?: string;
  value?: number;
  custom_parameter?: string;
  email?: string;
  user_id?: string;
  session_id?: string;
  page_title?: string;
  page_location?: string;
}

// Custom analytics events
export interface CustomAnalyticsEvents {
  join_waitlist: {
    email: string;
    source?: string;
    campaign?: string;
  };
  page_view: {
    page_title: string;
    page_location: string;
    page_path: string;
  };
  button_click: {
    button_name: string;
    button_location: string;
    page_path: string;
  };
  form_submit: {
    form_name: string;
    form_location: string;
    success: boolean;
  };
  external_link_click: {
    link_url: string;
    link_text: string;
    page_path: string;
  };
  video_play: {
    video_title: string;
    video_duration?: number;
    page_path: string;
  };
  accordion_toggle: {
    accordion_item: string;
    action: 'open' | 'close';
    page_path: string;
  };
}

// Google Analytics 4 Measurement Protocol types
export interface GA4Event {
  name: string;
  parameters: Record<string, any>;
}

export interface GA4Payload {
  client_id: string;
  user_id?: string;
  timestamp_micros?: number;
  user_properties?: Record<string, any>;
  events: GA4Event[];
}

// Firebase initialization options
export interface FirebaseInitOptions {
  config: FirebaseConfig;
  enableAnalytics?: boolean;
  analyticsConfig?: {
    send_page_view?: boolean;
    custom_map?: Record<string, string>;
  };
}

// Error types for Firebase
export interface FirebaseError {
  code: string;
  message: string;
  customData?: any;
}

// Firebase performance monitoring types
export interface PerformanceTrace {
  name: string;
  startTime: number;
  endTime?: number;
  attributes?: Record<string, string>;
  metrics?: Record<string, number>;
}

// Remote config types
export interface RemoteConfigValue {
  asBoolean(): boolean;
  asNumber(): number;
  asString(): string;
  getSource(): 'static' | 'default' | 'remote';
}

export interface RemoteConfigDefaults {
  [key: string]: string | number | boolean;
}
